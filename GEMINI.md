# Kontekst Gemini dla projektu WMS-System

Ten dokument dostarcza kontekstu dla projektu WMS-System, przeznaczonego dla Gemini.

## Przegląd Projektu

Projekt to System Zarządzania Magazynem (WMS) składający się z backendu napisanego w technologii .NET oraz aplikacji mobilnej na platformę Android, stworzonej w .NET MAUI. System jest przeznaczony dla urządzeń Zebra MC3300 i skupia się na dwóch głównych operacjach: logowaniu użytkownika poprzez skanowanie karty oraz zmianie lokalizacji palet.

Backend został zbudowany w oparciu o ASP.NET Core (.NET 9) z zachowaniem zasad Czystej Architektury (Clean Architecture). Jako bazę danych wykorzystuje MySQL. Aplikacja mobilna została stworzona w .NET MAUI dla systemu Android i komunikuje się z backendem za pomocą REST API.

Projekt zawiera również obszerną dokumentację, w tym dokumenty architektury, przewodniki po stylu kodowania oraz ADR-y (Architecture Decision Records).

## Budowanie i Uruchamianie

### Backend

Aby uruchomić backend, przejdź do katalogu `src/backend/Wms.Api` i wykonaj następujące polecenie:

```bash
dotnet run
```

### Frontend (Android)

Aby zbudować i uruchomić frontend, przejdź do katalogu `src/mobile/WmsApp` i wykonaj następujące polecenie:

```bash
dotnet build -t:Run -f net9.0-android
```

### Całe Rozwiązanie

Aby zbudować całe rozwiązanie, wykonaj następujące polecenie w głównym katalogu projektu:

```bash
dotnet build
```

### Testowanie

Aby uruchomić testy, wykonaj następujące polecenie w głównym katalogu projektu:

```bash
dotnet test
```

## Konwencje Programistyczne

### Styl Kodowania

Projekt używa pliku `.editorconfig` do egzekwowania spójnego stylu kodowania. Główne zasady to:

*   **C#:** Użycie tabulatorów do wcięć, z szerokością 4 spacji na tabulator.
*   **JSON:** Użycie 2 spacji do wcięć.
*   **YAML:** Użycie 2 spacji do wcięć.
*   **XML:** Użycie tabulatorów do wcięć, z szerokością 4 spacji na tabulator.

### Konwencje Nazewnictwa

*   **PascalCase:** dla klas, metod, publicznych właściwości i typów wyliczeniowych (enum).
*   **camelCase:** dla parametrów, zmiennych lokalnych i pól prywatnych.
*   **UPPER_SNAKE_CASE:** dla stałych i zmiennych środowiskowych.
*   **kebab-case:** dla plików konfiguracyjnych i adresów URL (endpointów).

### Architektura

Backend jest zgodny ze wzorcem Czystej Architektury (Clean Architecture) i podzielony na następujące warstwy:

*   **Wms.Domain:** Reguły biznesowe i encje.
*   **Wms.Application:** Reguły biznesowe aplikacji, przypadki użycia i interfejsy.
*   **Wms.Infrastructure:** Zewnętrzne zależności (baza danych, serwisy zewnętrzne).
*   **Wms.Api:** Warstwa prezentacji (kontrolery REST API).

### Testowanie

Projekt zawiera testy jednostkowe, integracyjne i testy API. Testy są pisane przy użyciu xUnit i Moq. Nazwy plików testowych są zgodne ze wzorcem `[NazwaKlasy]Tests.cs`. Nazwy metod testowych są zgodne ze wzorcem `NazwaMetody_TestowanyStan_OczekiwaneZachowanie`.
