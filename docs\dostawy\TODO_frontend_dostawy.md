# TODO Frontend – <PERSON><PERSON><PERSON> "Dostawy" (MAUI Android)

**Cel:** Implementacja widoków dostaw zgodnie z PRD_frontend_dostawy.md i wzorcami MAUI z ARCHITECTURE.md

## Faza 1 – Infrastruktura i Services (0%)
- [ ] **HTTP Client** (zgodnie z wzorcami z STYLE_GUIDE.md) (0%)
- [ ] `IReceiveApiClient` + implementacja
  - [ ] Retry policies dla krytycznych operacji
  - [ ] Timeout configuration (LAN environment)
- [ ] **Services** (Dependency Injection) (0%)
- [ ] `IReceiveService` (business logic)
  - [ ] `IPrintingService` (komunikacja z drukarkami) – normalizacja wejścia skanowanego: akceptuj prefiks „IP” (usuń), waliduj IPv4
  - [ ] `IGs1ScanningService` (parsowanie GS1)
  - [ ] `IAwizacjaService` – pobieranie pozycji awizacji dla wybranego LK

## Faza 2 – DataWedge Integration (zgodnie z ARCHITECTURE.md) (0%)
- [ ] **Android BroadcastReceiver** (0%)
- [ ] `ReceivesDataWedgeReceiver.cs` w `Platforms/Android/`
  - [ ] Intent filter dla skanów dostaw
  - [ ] MessagingCenter integration
- [ ] **Scan Processing** (0%)
  - [ ] Walidacja kodów (LK, SSCC, DS, GS1)
  - [ ] Routing skanów do odpowiednich ViewModels

## Faza 3 – ViewModels (MVVM + CommunityToolkit.Mvvm) (0%)
- [ ] **ReceivesSelectionViewModel** (0%)
- [ ] `[ObservableProperty]` dla `LkInput`, `AvailableReceives`
- [ ] `[RelayCommand]` dla `LoadReceives`, `ClaimReceive`
  - [ ] Walidacja LK format (bez zer wiodących)
  - [ ] Obsługa claim conflicts (409 responses)
- [ ] **ReceivesRegistrationViewModel** (0%)
  - [ ] Scan processing logic
  - [ ] Item registration workflow (obsługa pola `Certyfikat` oraz `DataPrzyjecia` – readonly)
  - [ ] Pallet completion handling
  - [ ] Enter w polu `Ilość` = zatwierdzenie i pozostanie na bieżącym nośniku (DS/SSCC)
  - [ ] Przełącznik źródła towaru: „Awizacja” | „Kartoteka” (współpraca z `IAwizacjaService`)

## Etap 3 – Widok 2: Dostawa rejestracja (0%)
- [ ] Pole „Skanuj” z autofocus i cyklem: skan → API → wynik → focus powrót. (0%)
- [ ] Prezentacja bieżącego nośnika (DS/SSCC) lub „(Auto)”. (0%)
- [ ] Sekcja towaru (Szukaj; selekcja przy wielu wynikach, w tym z pozycji awizacji). (0%)
- [ ] Pola dat/partii/ilości z przeliczeniami (AI 37 × ilosc_w_opakowaniu). (0%)
- [ ] Pole „Certyfikat” (jeśli wymagane) oraz widoczna, tylko do odczytu „Data przyjęcia”. (0%)
- [ ] Zmiana typu palety (edytowalna). (0%)
- [ ] „Podgląd” pozycji DS. (0%)
- [ ] „Nośnik kompletny” (zamyka bieżący DS). (0%)
- [ ] „Koniec” – zakończenie sesji i zwolnienie claim. (0%)
- [ ] Enter w polu „Ilość” = zatwierdzenie i pozostanie na bieżącym nośniku. (0%)

## Faza 4 – Views (XAML + Styles) (0%)
- [ ] **ReceivesSelectionPage** (zgodnie z STYLE_GUIDE.md) (0%)
  - [ ] Large touch targets (min. 44px)
  - [ ] LK input field z walidacją
  - [ ] Lista dostaw (CollectionView)
  - [ ] Generate DS modal/popup
- [ ] **ReceivesRegistrationPage** (0%)
  - [ ] Scan input (autofocus)
  - [ ] Item details form
  - [ ] Action buttons (proper spacing)
  - [ ] Toast/Alert components
- [ ] **Styles i Resources** (0%)
- [ ] Receives-specific styles w `Resources/Styles/`
  - [ ] Color scheme dla modułu dostaw

## Faza 5 – Navigation (MAUI Shell) (0%)
- [ ] **Shell Configuration** (0%)
  - [ ] Menu: „Przyjęcia” → „Opcje przyjęć” → „Rejestracja przyjęcia”
  - [ ] Routes dla receives pages
  - [ ] Navigation parameters
  - [ ] Back button handling
- [ ] **Deep Linking** (0%)
  - [ ] Direct navigation to specific LK

## Faza 6 – Error Handling i UX (0%)
- [ ] **Global Error Handling** (0%)
  - [ ] HTTP error responses (404, 409, 400)
  - [ ] Network connectivity issues
  - [ ] Toast notifications for success/error
- [ ] **Validation** (0%)
  - [ ] Client-side validation (IP format z prefiksem „IP” – strip + IPv4)
  - [ ] Client-side validation pól: Ilość, Certyfikat (jeśli wymagany)
  - [ ] Real-time feedback

## Faza 7 – Testing (0%)
- [ ] **Unit Tests** (0%)
  - [ ] ViewModels logic
  - [ ] Services business rules
  - [ ] Enter w polu Ilość (zachowanie fokus/pozostanie na DS)
  - [ ] Normalizacja skanu drukarki z prefiksem „IP”
  - [ ] Przełącznik „Awizacja | Kartoteka” – poprawność źródeł danych
- [ ] **Manual Testing** (0%)
  - [ ] Scenariusze nominal flows
  - [ ] Error scenarios
  - [ ] DataWedge integration
  - [ ] Certyfikat + Data przyjęcia (readonly) na UI

---
**Postęp całościowy: 0%**

### Struktura katalogów (zgodnie z STYLE_GUIDE.md):
```
WmsApp/
├── Views/
│   └── Receives/
│       ├── ReceivesSelectionPage.xaml
│       └── ReceivesRegistrationPage.xaml
├── ViewModels/
│   └── Receives/
│       ├── ReceivesSelectionViewModel.cs
│       └── ReceivesRegistrationViewModel.cs
├── Services/
│   └── Receives/
│       ├── IReceiveService.cs
│       └── ReceiveService.cs
├── Models/
│   └── Receives/
│       └── ReceiveModels.cs
└── Platforms/
    └── Android/
        └── ReceivesDataWedgeReceiver.cs
```
