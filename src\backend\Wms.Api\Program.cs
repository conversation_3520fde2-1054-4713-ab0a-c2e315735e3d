using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;
using Wms.Api.Extensions;
using Wms.Api.HealthChecks;
using Wms.Api.Metrics;
using Wms.Api.Middleware;
using Wms.Api.Services;
using Wms.Application.Extensions;
using Wms.Infrastructure.Extensions;
using Wms.Infrastructure.Interceptors;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
    .MinimumLevel.Override("System", Serilog.Events.LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/wms-api-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Use Serilog
    builder.Host.UseSerilog();

    // Add services to the container
    builder.Services
        .AddControllers();

    // PostConfigure to ensure removal happens after any other registrations
    builder.Services.PostConfigure<Microsoft.AspNetCore.Mvc.MvcOptions>(options =>
    {
        for (int i = options.ModelValidatorProviders.Count - 1; i >= 0; i--)
        {
            var provider = options.ModelValidatorProviders[i];
            var typeName = provider.GetType().FullName ?? string.Empty;
            if (typeName.StartsWith("FluentValidation"))
            {
                options.ModelValidatorProviders.RemoveAt(i);
            }
        }
    });

    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddOpenApi();

    // Custom extensions
    builder.Services.AddCustomApiVersioning();
    builder.Services.AddValidation();
    builder.Services.AddAutoMapper();
    builder.Services.AddCustomProblemDetails();

    // JWT Authentication
    var jwtSettings = builder.Configuration.GetSection("JwtSettings");
    builder.Services.AddAuthentication("Bearer")
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(
                    System.Text.Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]!)),
                ValidateIssuer = true,
                ValidIssuer = jwtSettings["Issuer"],
                ValidateAudience = true,
                ValidAudience = jwtSettings["Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };
        });

    // Application and Infrastructure layers
    builder.Services.AddApplication(builder.Configuration);
    builder.Services.AddInfrastructure(builder.Configuration);
    
    // Observability - WMS Metrics and Services
    builder.Services.AddSingleton<WmsMetrics>();
    builder.Services.AddHttpContextAccessor();
    builder.Services.AddScoped<ICorrelationIdService, CorrelationIdService>();
    builder.Services.AddBusinessEventLogging();
    builder.Services.AddRuntimePerformanceMonitoring();
    builder.Services.AddDatabasePerformanceLogging();
    
    // Enhanced Health Checks
    builder.Services.AddHealthChecks()
        .AddCheck<DatabaseHealthCheck>("database")
        .AddCheck<AuthenticationServiceHealthCheck>("authentication")
        .AddCheck<PalletServiceHealthCheck>("pallet_service");
    
    // OpenTelemetry configuration
    builder.Services.AddOpenTelemetry()
        .ConfigureResource(resource =>
            resource.AddService(
                serviceName: "WMS.Api",
                serviceVersion: "1.0.0",
                serviceInstanceId: Environment.MachineName))
        .WithTracing(tracing =>
            tracing
                .AddAspNetCoreInstrumentation()
                .AddEntityFrameworkCoreInstrumentation())
        .WithMetrics(metrics =>
            metrics
                .AddAspNetCoreInstrumentation()
                .AddMeter(WmsMetrics.MeterName)
                .AddPrometheusExporter());

    // CORS (for development)
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("Development", policy =>
        {
            policy.AllowAnyOrigin()
                  .AllowAnyMethod()
                  .AllowAnyHeader();
        });
    });

    var app = builder.Build();

    // Configure the HTTP request pipeline
    app.UseCorrelationId();
    app.UseRequestResponseLogging();
    app.UseMiddleware<GlobalExceptionMiddleware>();
    app.UseMiddleware<MetricsMiddleware>();

    if (app.Environment.IsDevelopment())
    {
        app.MapOpenApi();
        app.UseCors("Development");
    }
    else
    {
        app.UseHsts();
    }

    app.UseHttpsRedirection();
    app.UseAuthentication();
    app.UseAuthorization();

    app.MapControllers();
    
    // Enhanced Health check endpoints
    app.MapHealthChecks("/health");
    app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        Predicate = check => check.Tags.Contains("ready")
    });
    app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        Predicate = _ => false
    });
    
    // Prometheus metrics endpoint
    app.MapPrometheusScrapingEndpoint();

    Log.Information("Starting WMS API application");
    
    // Start runtime performance monitoring
    var performanceMonitor = app.Services.GetRequiredService<IRuntimePerformanceMonitor>();
    await performanceMonitor.StartMonitoringAsync();
    
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

// Make Program class accessible for integration tests
public partial class Program { }
