using MediatR;
using Wms.Application.DTOs.Receives;

namespace Wms.Application.Features.Receives.Queries;

public record GetAvailableReceivesQuery : IRequest<ReceiveListResponse>
{
    public int? MagazynId { get; init; }
    public int Limit { get; init; } = 100;
    public bool IncludeAssigned { get; init; } = false;
}

public record GetReceiveDetailsQuery : IRequest<ReceiveDetailsDto?>
{
    public int ReceiveId { get; init; }
}

public record GetReceiveItemsQuery : IRequest<List<ReceiveItemDto>>
{
    public int ListControlId { get; init; }
    public int? PaletaId { get; init; } // Jeś<PERSON> null, zwraca wszystkie pozycje dostawy
}

public record GetCarrierItemsQuery : IRequest<List<ReceiveItemDto>>
{
    public int ListControlId { get; init; }
    public int PaletaId { get; init; }
}

public record GetReceiveExpectedItemsQuery : IRequest<List<ExpectedItemDto>>
{
    public int ListControlId { get; init; }
}

public record GetPrintersQuery : IRequest<List<PrinterDto>>
{
    public bool OnlyActive { get; init; } = true;
}

public record GetReceiveCarriersQuery : IRequest<List<CarrierDto>>
{
    public int ListControlId { get; init; }
}
