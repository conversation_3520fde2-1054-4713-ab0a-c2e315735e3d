using Wms.Domain.Entities;

namespace Wms.Application.Interfaces;

public interface ILabelRepository
{
    Task<Label?> GetBySSCCAsync(string sscc);
    Task<Label?> GetBySSCCAsNoTrackingAsync(string sscc);
    Task<Label?> GetByClientCodeAsync(string clientCode);
    Task<Label?> GetByClientCodeAsNoTrackingAsync(string clientCode);
    Task<Label?> GetByPalletIdDirectAsync(int palletId);
    Task<IEnumerable<Label>> GetByPalletIdAsync(int palletId);
    Task<IEnumerable<Label>> GetActiveByPalletIdAsync(int palletId);
    Task<IEnumerable<Label>> GetActiveByPalletIdAsNoTrackingAsync(int palletId);
    Task UpdateLocationAsync(int labelId, int systemId, int newLocationId);
    void UpdateLocationDirect(Label label, int newLocationId);
    Task UpdateLocationByIdAsync(int labelId, int newLocationId);
}
