using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;

namespace Wms.Application.Interfaces;

public interface IPalletRepository
{
    Task<Pallet> CreatePalletAsync(int palletTypeId, CancellationToken cancellationToken = default);
    Task<ListControlPallet> CreateListControlPalletAsync(int listControlId, int paletaId, bool shouldPrint = true, CancellationToken cancellationToken = default);
    Task<IEnumerable<ListControlPallet>> GetCarriersForReceiveAsync(int listControlId, CancellationToken cancellationToken = default);
    Task<ListControlPallet?> GetCarrierAsync(int listControlId, int paletaId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Label>> GetCarrierItemsAsync(int paletaId, CancellationToken cancellationToken = default);
    Task<IEnumerable<TypyPalet>> GetTypyPaletAsync(CancellationToken cancellationToken = default);
    Task UpdateAsync(Pallet pallet, CancellationToken cancellationToken = default);
    Task UpdateListControlPalletAsync(ListControlPallet carrier, CancellationToken cancellationToken = default);
}
