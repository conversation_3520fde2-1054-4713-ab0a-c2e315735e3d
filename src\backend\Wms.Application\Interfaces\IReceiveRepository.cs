using Wms.Domain.Entities.Receives;

namespace Wms.Application.Interfaces;

public interface IReceiveRepository
{
    Task<IEnumerable<ListControl>> GetAvailableReceivesAsync(int? warehouseId = null, bool includeAssigned = false, CancellationToken cancellationToken = default);
    Task<ListControl?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<ListControl?> GetWithDetailsAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> ClaimReceiveAsync(int receiveId, int userId, CancellationToken cancellationToken = default);
    Task<bool> ReleaseReceiveAsync(int receiveId, int userId, CancellationToken cancellationToken = default);
    Task<bool> IsReceiveClaimedByUserAsync(int receiveId, int userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AwizacjaDane>> GetExpectedItemsAsync(int receiveId, CancellationToken cancellationToken = default);
    Task<Dictionary<string, (int KodId, string KodNazwa, string? Ean)>> GetKodLookupAsync(int systemId, IEnumerable<string> codes, CancellationToken cancellationToken = default);
    Task UpdateAsync(ListControl receive, CancellationToken cancellationToken = default);
}
