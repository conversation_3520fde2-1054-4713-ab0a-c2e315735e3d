using Wms.Domain.Common;
using Wms.Domain.Entities; // For Kod entity

namespace Wms.Domain.Entities.Receives;

/// <summary>
/// Encja reprezentująca pozycję awizacji dostawy
/// Mapowana na tabelę awizacje_dostaw_dane
/// </summary>
public class AwizacjaDane : BaseEntity
{
    public int Id { get; set; }
    public int AwizacjeDostawId { get; set; } = 0;
    public string EtykietaKlient { get; set; } = string.Empty; // SSCC
    public string Paletyzacja { get; set; } = string.Empty;
    public int Grupa { get; set; } = 0;
    public string Kod { get; set; } = string.Empty;
    public string Lot { get; set; } = string.Empty; // Partia z awizacji
    public string? Blloc { get; set; } // Certyfikat z awizacji
    public decimal? Ilosc { get; set; }
    public int PozycjaZamowienia { get; set; } = 0;
    public DateOnly? DataWaznosci { get; set; }
    public DateOnly? Dataprod { get; set; }
    public string? Krajprod { get; set; }
    
    // Navigation properties
    public AwizacjaHead AwizacjaHead { get; set; } = null!;
    public Kod? KodEntity { get; set; } // Powiązanie z kartoteką kodów
    
    // Business logic properties
    public bool HasSSCC => !string.IsNullOrEmpty(EtykietaKlient);
    public bool HasLot => !string.IsNullOrEmpty(Lot);
    public bool HasCertificate => !string.IsNullOrEmpty(Blloc);
    public bool HasExpiryDate => DataWaznosci.HasValue;
    public bool HasProductionDate => Dataprod.HasValue;
    
    /// <summary>
    /// Sprawdza czy pozycja awizacji została już przyjęta
    /// Logika: porównanie z etykietami gdzie etykieta_klient = EtykietaKlient
    /// </summary>
    public bool IsReceived()
    {
        // TODO: Implementacja sprawdzania w etykietach
        return false;
    }
    
    /// <summary>
    /// Zwraca opis pozycji dla UI
    /// </summary>
    public string GetDisplayDescription()
    {
        var parts = new List<string>();
        
        if (!string.IsNullOrEmpty(Kod))
            parts.Add($"Kod: {Kod}");
            
        if (Ilosc.HasValue)
            parts.Add($"Ilość: {Ilosc}");
            
        if (!string.IsNullOrEmpty(Lot))
            parts.Add($"Partia: {Lot}");
            
        return string.Join(" | ", parts);
    }
}
