using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;
using Wms.Domain.ValueObjects;

namespace Wms.Domain.Services;

/// <summary>
/// Domain Service do generowania nośników DS (palet) dla dostaw
/// Zawiera reguły biznesowe dotyczące tworzenia DS
/// </summary>
public class PalletGenerationService
{
    /// <summary>
    /// Waliduje parametry generowania palet DS
    /// </summary>
    public GenerationValidationResult ValidateGeneration(ListControl receive, int userId, 
        TypyPalet palletType, int quantity, string? printerIp = null, bool shouldPrint = false)
    {
        var errors = new List<string>();

        // Walidacja podstawowych parametrów
        if (receive == null)
            errors.Add("Dostawa jest wymagana");

        if (quantity <= 0)
            errors.Add("Ilość palet musi być większa od 0");

        if (quantity > 100)
            errors.Add("Nie można wygenerować więcej niż 100 palet jednocześnie");

        if (palletType == null)
            errors.Add("Typ palety jest wymagany");

        // Walidacja claim dostawy
        if (receive != null && (!receive.IsAssigned || receive.RealizujacyPracownikId != userId))
            errors.Add("Dostawa musi być przypisana do aktualnego pracownika");

        // Walidacja typu palety
        if (palletType != null && !palletType.IsValidForDSGeneration())
            errors.Add($"Typ palety '{palletType.Opis}' nie może być używany do generowania DS");

        // Walidacja drukarki jeśli drukowanie jest włączone
        if (shouldPrint)
        {
            if (string.IsNullOrWhiteSpace(printerIp))
                errors.Add("IP drukarki jest wymagane gdy drukowanie jest włączone");
            else if (!IsValidPrinterIp(printerIp))
                errors.Add($"Nieprawidłowe IP drukarki: {printerIp}");
        }

        return errors.Count == 0 
            ? GenerationValidationResult.Success()
            : GenerationValidationResult.Failed(errors);
    }

    /// <summary>
    /// Tworzy specyfikację dla generowania palet DS
    /// </summary>
    public PalletGenerationSpec CreateGenerationSpec(ListControl receive, int userId, 
        TypyPalet palletType, int quantity, string? printerIp = null, bool shouldPrint = false)
    {
        var validation = ValidateGeneration(receive, userId, palletType, quantity, printerIp, shouldPrint);
        if (!validation.IsSuccess)
            throw new ArgumentException($"Nieprawidłowe parametry generowania: {string.Join(", ", validation.Errors)}");

        return new PalletGenerationSpec
        {
            ReceiveId = receive.Id,
            UserId = userId,
            PalletTypeId = palletType.Id,
            Quantity = quantity,
            PrinterIp = shouldPrint ? printerIp : null,
            ShouldPrint = shouldPrint,
            CreatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Waliduje pojedynczy numer DS
    /// </summary>
    public bool IsValidDSNumber(int dsNumber)
    {
        return dsNumber >= 1000 && dsNumber <= 999999999; // 4-9 cyfr
    }

    /// <summary>
    /// Tworzy DSCode z numeru (formatuje do DS + 8 cyfr)
    /// </summary>
    public DSCode CreateDSCode(int dsNumber)
    {
        var dsString = $"DS{dsNumber:D8}"; // Format to 8 digits with leading zeros
        return DSCode.Create(dsString);
    }

    /// <summary>
    /// Sprawdza czy można wydrukować DS
    /// </summary>
    public bool CanPrintDS(DSCode dsCode, string printerIp)
    {
        if (dsCode == null) return false;
        if (string.IsNullOrWhiteSpace(printerIp)) return false;
        if (!IsValidPrinterIp(printerIp)) return false;

        return true;
    }

    /// <summary>
    /// Waliduje IP drukarki (akceptuje prefiks "IP")
    /// </summary>
    private bool IsValidPrinterIp(string printerIp)
    {
        if (string.IsNullOrWhiteSpace(printerIp))
            return false;

        // Usuń prefiks "IP" jeśli istnieje
        var cleanIp = printerIp.StartsWith("IP", StringComparison.OrdinalIgnoreCase) 
            ? printerIp[2..] 
            : printerIp;

        // Walidacja IPv4
        if (!System.Net.IPAddress.TryParse(cleanIp, out var ipAddress))
            return false;

        // Sprawdź czy to IPv4
        return ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork;
    }

    /// <summary>
    /// Normalizuje IP drukarki (usuwa prefiks "IP")
    /// </summary>
    public string NormalizePrinterIp(string printerIp)
    {
        if (string.IsNullOrWhiteSpace(printerIp))
            throw new ArgumentException("IP drukarki nie może być pusty");

        var cleanIp = printerIp.StartsWith("IP", StringComparison.OrdinalIgnoreCase) 
            ? printerIp[2..] 
            : printerIp;

        if (!IsValidPrinterIp(cleanIp))
            throw new ArgumentException($"Nieprawidłowe IP drukarki: {printerIp}");

        return cleanIp;
    }

    /// <summary>
    /// Generuje nowy kod DS
    /// </summary>
    public DSCode GenerateDSCode()
    {
        var random = new Random();
        var dsNumber = random.Next(10000000, 99999999); // 8 digits
        return DSCode.Create($"DS{dsNumber}");
    }

    /// <summary>
    /// Generuje nowy kod SSCC
    /// </summary>
    public SSCCCode GenerateSSCCCode()
    {
        var random = new Random();
        
        // Generate 18 digit SSCC
        var ssccBuilder = new System.Text.StringBuilder();
        for (int i = 0; i < 18; i++)
        {
            ssccBuilder.Append(random.Next(0, 10));
        }
        
        return SSCCCode.Create(ssccBuilder.ToString());
    }

    /// <summary>
    /// Tworzy nową paletę z podanymi parametrami
    /// </summary>
    public CreatePalletResult CreatePallet(DSCode dsCode, SSCCCode ssccCode, string location, 
        int palletTypeId, int userId, DateTime createdAt)
    {
        if (dsCode == null)
            return CreatePalletResult.Failed("Kod DS jest wymagany");

        if (ssccCode == null)
            return CreatePalletResult.Failed("Kod SSCC jest wymagany");

        if (string.IsNullOrWhiteSpace(location))
            return CreatePalletResult.Failed("Lokalizacja jest wymagana");

        if (palletTypeId <= 0)
            return CreatePalletResult.Failed("Typ palety musi być większy od 0");

        if (userId <= 0)
            return CreatePalletResult.Failed("ID użytkownika musi być większe od 0");

        var label = new Label
        {
            Sscc = ssccCode.Value,
            EtykietaKlient = dsCode.Value, // Store DS code as client label
            Miejscep = 0, // Location to be set later
            PaletaId = null, // To be assigned when pallet is created
            Ts = createdAt,
            Active = 1
        };

        return CreatePalletResult.Success(label);
    }

    /// <summary>
    /// Sprawdza czy można wygenerować paletę dla danego typu
    /// </summary>
    public CanGeneratePalletResult CanGeneratePallet(TypyPalet palletType)
    {
        if (palletType == null)
            return CanGeneratePalletResult.Failed("Typ palety nie został znaleziony");

        if (!IsValidPalletType(palletType))
            return CanGeneratePalletResult.Failed($"Typ palety '{palletType.Opis}' nie może być używany do generowania DS");

        return CanGeneratePalletResult.Success();
    }

    /// <summary>
    /// Sprawdza czy typ palety jest prawidłowy do generowania DS
    /// </summary>
    public static bool IsValidPalletType(TypyPalet palletType)
    {
        if (palletType == null)
            return false;

        if (string.IsNullOrWhiteSpace(palletType.Opis))
            return false;

        if (palletType.UdzialSkladowania <= 0)
            return false;

        return true;
    }
}

/// <summary>
/// Specyfikacja generowania palet DS
/// </summary>
public record PalletGenerationSpec
{
    public int ReceiveId { get; init; }
    public int UserId { get; init; }
    public int PalletTypeId { get; init; }
    public int Quantity { get; init; }
    public string? PrinterIp { get; init; }
    public bool ShouldPrint { get; init; }
    public DateTime CreatedAt { get; init; }
}

/// <summary>
/// Wynik walidacji generowania palet
/// </summary>
public record GenerationValidationResult(bool IsSuccess, List<string> Errors)
{
    public static GenerationValidationResult Success() => new(true, new List<string>());
    public static GenerationValidationResult Failed(List<string> errors) => new(false, errors);
    public static GenerationValidationResult Failed(string error) => new(false, new List<string> { error });
    
    public string ErrorMessage => string.Join("; ", Errors);
}

/// <summary>
/// Wynik tworzenia palety
/// </summary>
public record CreatePalletResult(bool IsSuccess, string? ErrorMessage, Label? Pallet)
{
    public static CreatePalletResult Success(Label label) => new(true, null, label);
    public static CreatePalletResult Failed(string error) => new(false, error, null);
}

/// <summary>
/// Wynik sprawdzania możliwości generowania palety
/// </summary>
public record CanGeneratePalletResult(bool IsSuccess, string? ErrorMessage)
{
    public static CanGeneratePalletResult Success() => new(true, null);
    public static CanGeneratePalletResult Failed(string error) => new(false, error);
}
