using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;
using Wms.Domain.ValueObjects;

namespace Wms.Domain.Services;

/// <summary>
/// Domain Service dla zarządzania regułami biznesowymi dostaw
/// Zawiera logikę claim/release i walidacje dostaw
/// </summary>
public class ReceiveDomainService
{
    /// <summary>
    /// Sprawdza czy dostawa może być zajęta przez pracownika
    /// </summary>
    public CanClaimResult CanClaimReceive(ListControl receive, int userId)
    {
        // Sprawdzenie czy dostawa istnieje
        if (receive == null)
            return CanClaimResult.Failed("Dostawa nie została znaleziona");

        // Sprawdzenie czy już jest zajęta
        if (receive.IsAssigned)
        {
            if (receive.RealizujacyPracownikId == userId)
                return CanClaimResult.Failed("Dostawa jest już zajęta przez tego pracownika");
            
            return CanClaimResult.Failed($"Dostawa jest zajęta przez pracownika ID: {receive.RealizujacyPracownikId}");
        }

        // Sprawdzenie czy dostawa jest gotowa do obsługi
        if (receive.IsCompleted)
            return CanClaimResult.Failed("Dostawa została już ukończona");

        return CanClaimResult.Success();
    }

    /// <summary>
    /// Zajmuje dostawę dla pracownika
    /// </summary>
    public ClaimResult ClaimReceive(ListControl receive, int userId, DateTime claimedAt)
    {
        var canClaim = CanClaimReceive(receive, userId);
        if (!canClaim.IsSuccess)
            return ClaimResult.Failed(canClaim.ErrorMessage!);

        // Wykonanie claim
        receive.RealizujacyPracownikId = userId;
        
        return ClaimResult.Success(claimedAt);
    }

    /// <summary>
    /// Sprawdza czy dostawa może być zwolniona przez pracownika
    /// </summary>
    public CanReleaseResult CanReleaseReceive(ListControl receive, int userId)
    {
        if (receive == null)
            return CanReleaseResult.Failed("Dostawa nie została znaleziona");

        if (!receive.IsAssigned)
            return CanReleaseResult.Failed("Dostawa nie jest przypisana do żadnego pracownika");

        if (receive.RealizujacyPracownikId != userId)
            return CanReleaseResult.Failed($"Dostawa jest przypisana do innego pracownika (ID: {receive.RealizujacyPracownikId})");

        return CanReleaseResult.Success();
    }

    /// <summary>
    /// Zwalnia dostawę
    /// </summary>
    public ReleaseResult ReleaseReceive(ListControl receive, int userId, DateTime releasedAt)
    {
        var canRelease = CanReleaseReceive(receive, userId);
        if (!canRelease.IsSuccess)
            return ReleaseResult.Failed(canRelease.ErrorMessage!);

        // Wykonanie release
        receive.RealizujacyPracownikId = null;
        
        return ReleaseResult.Success(releasedAt);
    }

    /// <summary>
    /// Sprawdza czy wszystkie pozycje awizacji zostały przyjęte (kompletność dostawy)
    /// </summary>
    public bool IsReceiveComplete(ListControl receive, IEnumerable<AwizacjaDane> expectedItems, 
        IEnumerable<Label> receivedLabels)
    {
        if (!expectedItems.Any())
            return true; // Brak awizacji - dostawa może być uznana za kompletną

        // Sprawdzenie czy wszystkie pozycje awizacji mają odpowiadające etykiety
        foreach (var expectedItem in expectedItems)
        {
            if (!string.IsNullOrEmpty(expectedItem.EtykietaKlient))
            {
                // Sprawdzenie po SSCC
                var matchingLabel = receivedLabels.FirstOrDefault(l => 
                    l.Sscc == expectedItem.EtykietaKlient && l.ListcontrolId == receive.Id);
                
                if (matchingLabel == null)
                    return false; // Brakuje pozycji
            }
        }

        return true;
    }

    /// <summary>
    /// Waliduje czy można utworzyć nowy nośnik DS dla dostawy
    /// </summary>
    public CanCreatePalletResult CanCreatePallet(ListControl receive, int userId, TypyPalet palletType)
    {
        if (receive == null)
            return CanCreatePalletResult.Failed("Dostawa nie została znaleziona");

        if (!receive.IsAssigned || receive.RealizujacyPracownikId != userId)
            return CanCreatePalletResult.Failed("Dostawa musi być przypisana do pracownika");

        if (palletType == null)
            return CanCreatePalletResult.Failed("Typ palety jest wymagany");

        if (!palletType.IsValidForDSGeneration())
            return CanCreatePalletResult.Failed($"Typ palety '{palletType.Opis}' nie może być używany do generowania DS");

        return CanCreatePalletResult.Success();
    }

    /// <summary>
    /// Waliduje LK code
    /// </summary>
    public static bool IsValidLKCode(string code)
    {
        return LKCode.IsValid(code);
    }
}

// Result Types
public record CanClaimResult(bool IsSuccess, string? ErrorMessage)
{
    public static CanClaimResult Success() => new(true, null);
    public static CanClaimResult Failed(string error) => new(false, error);
}

public record ClaimResult(bool IsSuccess, string? ErrorMessage, DateTime? ClaimedAt)
{
    public static ClaimResult Success(DateTime claimedAt) => new(true, null, claimedAt);
    public static ClaimResult Failed(string error) => new(false, error, null);
}

public record CanReleaseResult(bool IsSuccess, string? ErrorMessage)
{
    public static CanReleaseResult Success() => new(true, null);
    public static CanReleaseResult Failed(string error) => new(false, error);
}

public record ReleaseResult(bool IsSuccess, string? ErrorMessage, DateTime? ReleasedAt)
{
    public static ReleaseResult Success(DateTime releasedAt) => new(true, null, releasedAt);
    public static ReleaseResult Failed(string error) => new(false, error, null);
}

public record CanCreatePalletResult(bool IsSuccess, string? ErrorMessage)
{
    public static CanCreatePalletResult Success() => new(true, null);
    public static CanCreatePalletResult Failed(string error) => new(false, error);
}
