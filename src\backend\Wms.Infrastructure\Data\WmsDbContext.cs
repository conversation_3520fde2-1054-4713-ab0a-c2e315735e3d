using Microsoft.EntityFrameworkCore;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;

namespace Wms.Infrastructure.Data;

public class WmsDbContext : DbContext
{
    public WmsDbContext(DbContextOptions<WmsDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Session> Sessions { get; set; }
    public DbSet<Location> Locations { get; set; }
    public DbSet<Pallet> Pallets { get; set; }
    public DbSet<Label> Labels { get; set; }
    public DbSet<Movement> Movements { get; set; }
    
    // Receives module entities
    public DbSet<ListControl> ListControls { get; set; }
    public DbSet<ListControlPallet> ListControlPallets { get; set; }
    public DbSet<AwizacjaHead> AwizacjaHeads { get; set; }
    public DbSet<AwizacjaDane> AwizacjaDanes { get; set; }
    public DbSet<Kod> <PERSON><PERSON> { get; set; }
    public DbSet<TypyPalet> TypyPalets { get; set; }
    public DbSet<Wms.Domain.Entities.System> Systems { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // User -> pracownicy table
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("pracownicy");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Login).HasColumnName("login").HasMaxLength(30);
            entity.Property(e => e.JednostkaId).HasColumnName("jednostka_id");
            entity.Property(e => e.ImieNazwisko).HasColumnName("imie_nazwisko").HasMaxLength(200);
            entity.Property(e => e.Haslo).HasColumnName("haslo").HasMaxLength(255);
            entity.Property(e => e.TelefonKom).HasColumnName("telefon_kom").HasMaxLength(25);
            entity.Property(e => e.Email).HasColumnName("email").HasMaxLength(200);
            entity.Property(e => e.Telefon).HasColumnName("telefon").HasMaxLength(25);
            entity.Property(e => e.Stanowisko).HasColumnName("stanowisko").HasMaxLength(45);
            entity.Property(e => e.NumerKarty).HasColumnName("numer_Karty").HasMaxLength(45);
            entity.Property(e => e.Pin).HasColumnName("pin");
            entity.Property(e => e.NumerRfid).HasColumnName("numer_rfid").HasMaxLength(45);
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            entity.Ignore(e => e.IsActive); // Can be added later as new column
            
            // Indexes
            entity.HasIndex(e => e.NumerKarty);
            entity.HasIndex(e => e.Login);
            entity.HasIndex(e => e.NumerRfid);
        });

        // Location -> miejsca table
        modelBuilder.Entity<Location>(entity =>
        {
            entity.ToTable("miejsca");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Hala).HasColumnName("hala");
            entity.Property(e => e.Regal).HasColumnName("regal").HasMaxLength(10);
            entity.Property(e => e.Miejsce).HasColumnName("miejsce");
            entity.Property(e => e.Poziom).HasColumnName("poziom").HasMaxLength(4);
            entity.Property(e => e.Wysokosc).HasColumnName("wysokosc").HasMaxLength(3);
            entity.Property(e => e.Widoczne).HasColumnName("widoczne");
            entity.Property(e => e.Zbiorka).HasColumnName("zbiorka");
            entity.Property(e => e.MaxPojemnosc).HasColumnName("max_pojemnosc");
            entity.Property(e => e.StanowiskoPracyId).HasColumnName("stanowisko_pracy_id");
            entity.Property(e => e.PickingFakturowany).HasColumnName("picking_fakturowany");
            entity.Property(e => e.Producent).HasColumnName("producent").HasMaxLength(45);
            entity.Property(e => e.MaxUdzwigKg).HasColumnName("max_udzwig_kg");
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            entity.Ignore(e => e.Code); // Computed property
            
            // Indexes
            entity.HasIndex(e => e.Regal);
            entity.HasIndex(e => e.Miejsce);
            entity.HasIndex(e => e.Poziom);
            entity.HasIndex(e => e.Hala);
        });

        // Pallet -> palety table
        modelBuilder.Entity<Pallet>(entity =>
        {
            entity.ToTable("palety");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.TypypaletId).HasColumnName("typypalet_id");
            entity.Property(e => e.Ilosc).HasColumnName("ilosc");
            entity.Property(e => e.JSkladowaniaId).HasColumnName("j_skladowania_id");
            entity.Property(e => e.PalKlient).HasColumnName("pal_klient").HasMaxLength(45);
            entity.Property(e => e.TsInwentaryzacja).HasColumnName("ts_inwentaryzacja");
            entity.Property(e => e.ResultInwentaryzacja).HasColumnName("result_inwentaryzacja").HasMaxLength(45);
            entity.Property(e => e.TsUtworzenia).HasColumnName("ts_utworzenia");
            entity.Property(e => e.PalDocinId).HasColumnName("pal_docin_id");
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
        });

        // Label -> etykiety table
        modelBuilder.Entity<Label>(entity =>
        {
            entity.ToTable("etykiety");
            entity.HasKey(e => new { e.Id, e.SystemId }); // Composite primary key
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.SystemId).HasColumnName("system_id");
            entity.Property(e => e.EtykietaKlient).HasColumnName("etykieta_klient").HasMaxLength(45);
            entity.Property(e => e.Magazyn).HasColumnName("magazyn");
            entity.Property(e => e.Active).HasColumnName("active");
            entity.Property(e => e.Miejscep).HasColumnName("miejscep");
            entity.Property(e => e.KodId).HasColumnName("kod_id");
            entity.Property(e => e.StatusIdOld).HasColumnName("status_id_old");
            entity.Property(e => e.StatusId).HasColumnName("status_id");
            entity.Property(e => e.Stat).HasColumnName("stat").HasMaxLength(17);
            entity.Property(e => e.PaletaId).HasColumnName("paleta_id");
            entity.Property(e => e.Kartony).HasColumnName("kartony");
            entity.Property(e => e.Dataprod).HasColumnName("dataprod");
            entity.Property(e => e.DataWaznosci).HasColumnName("data_waznosci");
            entity.Property(e => e.Ilosc).HasColumnName("ilosc").HasPrecision(10, 3);
            entity.Property(e => e.Ts).HasColumnName("ts");
            entity.Property(e => e.Status).HasColumnName("status").HasMaxLength(30);
            entity.Property(e => e.Blloc).HasColumnName("blloc").HasMaxLength(45);
            entity.Property(e => e.AkcjaId).HasColumnName("akcja_id");
            entity.Property(e => e.StatusPrism).HasColumnName("status_prism").HasMaxLength(45);
            entity.Property(e => e.Lot).HasColumnName("lot").HasMaxLength(300);
            entity.Property(e => e.Sscc).HasColumnName("sscc").HasMaxLength(35);
            entity.Property(e => e.Gtin).HasColumnName("gtin").HasMaxLength(24);
            entity.Property(e => e.PrzeznczenieId).HasColumnName("przeznaczenie_id");
            entity.Property(e => e.Nretykiety).HasColumnName("nretykiety");
            entity.Property(e => e.DocinId).HasColumnName("docin_id");
            entity.Property(e => e.DocoutId).HasColumnName("docout_id");
            entity.Property(e => e.DeliveryId).HasColumnName("delivery_id");
            entity.Property(e => e.ListcontrolId).HasColumnName("listcontrol_id");
            entity.Property(e => e.StatusId2).HasColumnName("status_id2");
            entity.Property(e => e.EdycjaEt).HasColumnName("edycja_et");
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            
            // Relationships
            entity.HasOne(e => e.Pallet)
                  .WithMany(p => p.Labels)
                  .HasForeignKey(e => e.PaletaId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.Location)
                  .WithMany(l => l.Labels)
                  .HasForeignKey(e => e.Miejscep)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // Movement -> zmianym table
        modelBuilder.Entity<Movement>(entity =>
        {
            entity.ToTable("zmianym");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Typ).HasColumnName("typ").HasMaxLength(5);
            entity.Property(e => e.DocNr).HasColumnName("doc_nr");
            entity.Property(e => e.PracownikId).HasColumnName("pracownik_id");
            entity.Property(e => e.Data).HasColumnName("data");
            entity.Property(e => e.Etykieta).HasColumnName("etykieta");
            entity.Property(e => e.SystemId).HasColumnName("system_id");
            entity.Property(e => e.StareM).HasColumnName("stare_m");
            entity.Property(e => e.NoweM).HasColumnName("nowe_m");
            entity.Property(e => e.DocInternal).HasColumnName("doc_internal").HasMaxLength(2);
            entity.Property(e => e.Stat).HasColumnName("stat");
            entity.Property(e => e.Tszm).HasColumnName("tszm");
            entity.Property(e => e.Uwagi).HasColumnName("uwagi").HasMaxLength(45);
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            
            // Relationships
            entity.HasOne(e => e.User)
                  .WithMany(u => u.Movements)
                  .HasForeignKey(e => e.PracownikId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.Label)
                  .WithMany()
                  .HasForeignKey(e => new { e.Etykieta, e.SystemId })
                  .HasPrincipalKey(l => new { l.Id, l.SystemId })
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.FromLocation)
                  .WithMany(l => l.MovementsFrom)
                  .HasForeignKey(e => e.StareM)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.ToLocation)
                  .WithMany(l => l.MovementsTo)
                  .HasForeignKey(e => e.NoweM)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // Session table (nowa tabela)
        modelBuilder.Entity<Session>(entity =>
        {
            entity.ToTable("sessions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.DeviceId).HasMaxLength(100);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.JwtTokenId).HasMaxLength(50);
            
            // Relationships
            entity.HasOne(e => e.User)
                  .WithMany(u => u.Sessions)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            // Indexes
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.JwtTokenId);
        });

        // ==== RECEIVES MODULE ENTITIES ====
        ConfigureReceivesEntities(modelBuilder);
        
        base.OnModelCreating(modelBuilder);
    }
    
    private void ConfigureReceivesEntities(ModelBuilder modelBuilder)
    {
        // ListControl -> list_control table
        modelBuilder.Entity<ListControl>(entity =>
        {
            entity.ToTable("list_control");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ListcontrolSystemId).HasColumnName("listcontrol_system_id");
            entity.Property(e => e.Numer).HasColumnName("numer");
            entity.Property(e => e.Data).HasColumnName("data");
            entity.Property(e => e.DokumentDostawy).HasColumnName("dokument_dostawy").HasMaxLength(160);
            entity.Property(e => e.Transport).HasColumnName("transport").HasMaxLength(145);
            entity.Property(e => e.Uwagi).HasColumnName("uwagi").HasMaxLength(145);
            entity.Property(e => e.ListInternal).HasColumnName("list_internal");
            entity.Property(e => e.Ts).HasColumnName("ts");
            entity.Property(e => e.PracownikId).HasColumnName("pracownik_id");
            entity.Property(e => e.ListcontrolTypeId).HasColumnName("listcontrol_type_id");
            entity.Property(e => e.AwizacjeId).HasColumnName("awizacje_id");
            entity.Property(e => e.IloscEtykiet).HasColumnName("ilosc_etykiet");
            entity.Property(e => e.MiejsceId).HasColumnName("miejsce_id");
            entity.Property(e => e.WdPrzyjecieHeadId).HasColumnName("wd_przyjecie_head_id");
            entity.Property(e => e.ListControlcol).HasColumnName("list_controlcol").HasMaxLength(45);
            entity.Property(e => e.PaletyOpisWydruk).HasColumnName("palety_opis_wydruk").HasMaxLength(45);
            entity.Property(e => e.RealizujacyPracownikId).HasColumnName("realizujacy_pracownik_id"); // NOWA KOLUMNA
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            
            // Relationships
            entity.HasOne(e => e.RealizujacyPracownik)
                  .WithMany()
                  .HasForeignKey(e => e.RealizujacyPracownikId)
                  .OnDelete(DeleteBehavior.SetNull);
                  
            entity.HasOne(e => e.Pracownik)
                  .WithMany()
                  .HasForeignKey(e => e.PracownikId)
                  .OnDelete(DeleteBehavior.SetNull);
                  
            entity.HasOne(e => e.MiejsceDostawy)
                  .WithMany()
                  .HasForeignKey(e => e.MiejsceId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.SystemEntity)
                  .WithMany()
                  .HasForeignKey(e => e.ListcontrolSystemId)
                  .OnDelete(DeleteBehavior.Restrict);
            
            // Indexes
            entity.HasIndex(e => e.RealizujacyPracownikId);
            entity.HasIndex(e => e.ListcontrolSystemId);
            entity.HasIndex(e => e.ListcontrolTypeId);
        });

        // ListControlPallet -> listcontrol_palety table
        modelBuilder.Entity<ListControlPallet>(entity =>
        {
            entity.ToTable("listcontrol_palety");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ListcontrolId).HasColumnName("listcontrol_id");
            entity.Property(e => e.PaletaId).HasColumnName("paleta_id"); // DS number from docnumber
            entity.Property(e => e.Wydruk).HasColumnName("wydruk");
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            
            // Relationships
            entity.HasOne(e => e.ListControl)
                  .WithMany(lc => lc.ListControlPallets)
                  .HasForeignKey(e => e.ListcontrolId)
                  .OnDelete(DeleteBehavior.Cascade);
            
            // Indexes
            entity.HasIndex(e => e.ListcontrolId);
            entity.HasIndex(e => e.PaletaId);
            entity.HasIndex(e => new { e.ListcontrolId, e.PaletaId }).IsUnique();
        });

        // AwizacjaHead -> awizacje_dostaw_head table
        modelBuilder.Entity<AwizacjaHead>(entity =>
        {
            entity.ToTable("awizacje_dostaw_head");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.SystemId).HasColumnName("system_id");
            entity.Property(e => e.Magazyn).HasColumnName("magazyn");
            entity.Property(e => e.Data).HasColumnName("data").HasMaxLength(12);
            entity.Property(e => e.Godzina).HasColumnName("godzina").HasMaxLength(10);
            entity.Property(e => e.Ts).HasColumnName("ts");
            entity.Property(e => e.PracownikId).HasColumnName("pracownik_id");
            entity.Property(e => e.Transport).HasColumnName("transport").HasMaxLength(45);
            entity.Property(e => e.Kierowca).HasColumnName("kierowca").HasMaxLength(45);
            entity.Property(e => e.Uwagi).HasColumnName("uwagi").HasMaxLength(45);
            entity.Property(e => e.Typ).HasColumnName("typ");
            entity.Property(e => e.NazwaPliku).HasColumnName("nazwa_pliku").HasMaxLength(45);
            entity.Property(e => e.KontrahId).HasColumnName("kontrah_id");
            entity.Property(e => e.NumerZamowienia).HasColumnName("numer_zamowienia").HasMaxLength(155);
            entity.Property(e => e.ListcontrolId).HasColumnName("listcontrol_id");
            entity.Property(e => e.Status).HasColumnName("status");
            entity.Property(e => e.Zleceniodawca).HasColumnName("zleceniodawca");
            entity.Property(e => e.DocinIdMan).HasColumnName("docin_id_man");
            entity.Property(e => e.NumerZamowieniaKlienta).HasColumnName("numer_zamowienia_klienta").HasMaxLength(2000);
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            
            // Relationships
            entity.HasOne(e => e.ListControl)
                  .WithMany()
                  .HasForeignKey(e => e.ListcontrolId)
                  .OnDelete(DeleteBehavior.SetNull);
                  
            entity.HasOne(e => e.Pracownik)
                  .WithMany()
                  .HasForeignKey(e => e.PracownikId)
                  .OnDelete(DeleteBehavior.SetNull);
            
            // Indexes
            entity.HasIndex(e => e.SystemId);
            entity.HasIndex(e => e.ListcontrolId);
            entity.HasIndex(e => e.DocinIdMan);
        });

        // AwizacjaDane -> awizacje_dostaw_dane table
        modelBuilder.Entity<AwizacjaDane>(entity =>
        {
            entity.ToTable("awizacje_dostaw_dane");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.AwizacjeDostawId).HasColumnName("awizacje_dostaw_id");
            entity.Property(e => e.EtykietaKlient).HasColumnName("etykieta_klient").HasMaxLength(45);
            entity.Property(e => e.Paletyzacja).HasColumnName("paletyzacja").HasMaxLength(45);
            entity.Property(e => e.Grupa).HasColumnName("grupa");
            entity.Property(e => e.Kod).HasColumnName("kod").HasMaxLength(90);
            entity.Property(e => e.Lot).HasColumnName("lot").HasMaxLength(45);
            entity.Property(e => e.Blloc).HasColumnName("blloc").HasMaxLength(45);
            entity.Property(e => e.Ilosc).HasColumnName("ilosc").HasPrecision(10, 3);
            entity.Property(e => e.PozycjaZamowienia).HasColumnName("pozycja_zamowienia");
            entity.Property(e => e.DataWaznosci).HasColumnName("data_waznosci");
            entity.Property(e => e.Dataprod).HasColumnName("dataprod");
            entity.Property(e => e.Krajprod).HasColumnName("krajprod").HasMaxLength(10);
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);

            // Ważne: ignorujemy nawigację KodEntity, aby EF nie tworzył shadow FK 'KodEntityId'
            entity.Ignore(e => e.KodEntity);
            // Oraz ignorujemy shadow property 'KodId', które EF konwencyjnie tworzy przy wykryciu encji Kod
            entity.Ignore("KodId");
            
            // Relationships
            entity.HasOne(e => e.AwizacjaHead)
                  .WithMany(ah => ah.AwizacjaDane)
                  .HasForeignKey(e => e.AwizacjeDostawId)
                  .OnDelete(DeleteBehavior.Cascade);
            
            // Indexes
            entity.HasIndex(e => e.AwizacjeDostawId);
            entity.HasIndex(e => e.EtykietaKlient);
            entity.HasIndex(e => e.Kod);
        });

        // Kod -> kody table
        modelBuilder.Entity<Kod>(entity =>
        {
            entity.ToTable("kody");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.KodValue).HasColumnName("kod").HasMaxLength(100);
            entity.Property(e => e.Kod2).HasColumnName("kod2").HasMaxLength(100);
            entity.Property(e => e.KodNazwa).HasColumnName("kod_nazwa").HasMaxLength(200);
            entity.Property(e => e.SystemId).HasColumnName("system_id");
            entity.Property(e => e.Jm).HasColumnName("jm").HasMaxLength(8);
            entity.Property(e => e.EanJednostki).HasColumnName("ean_jednostki").HasMaxLength(240);
            entity.Property(e => e.WagaSztKg).HasColumnName("waga_szt_kg").HasPrecision(9, 3);
            entity.Property(e => e.ObjetoscSztCm).HasColumnName("objetosc_szt_cm");
            entity.Property(e => e.Ean).HasColumnName("ean").HasMaxLength(100);
            entity.Property(e => e.OpakowanieJm).HasColumnName("opakowanie_jm").HasMaxLength(8);
            entity.Property(e => e.IloscWOpakowaniu).HasColumnName("ilosc_w_opakowaniu");
            entity.Property(e => e.EanOpakowanieZbiorcze).HasColumnName("ean_opakowanie_zbiorcze").HasMaxLength(45);
            entity.Property(e => e.IloscSztWZbiorczym).HasColumnName("ilosc_szt_w_zbiorczym");
            entity.Property(e => e.KodProducent).HasColumnName("kod_producent").HasMaxLength(75);
            entity.Property(e => e.IloscOpakWZbiorczym).HasColumnName("ilosc_opak_w_zborczym");
            entity.Property(e => e.KodKategoria).HasColumnName("kod_kategoria").HasMaxLength(45);
            entity.Property(e => e.KodPlec).HasColumnName("kod_plec").HasMaxLength(45);
            entity.Property(e => e.Active).HasColumnName("active");
            entity.Property(e => e.Gln).HasColumnName("gln").HasMaxLength(45);
            entity.Property(e => e.Wlasciciel).HasColumnName("wlasciciel").HasMaxLength(45);
            entity.Property(e => e.BrandId).HasColumnName("brand_id").HasMaxLength(45);
            entity.Property(e => e.KodyGrupyId).HasColumnName("kody_grupy_id");
            entity.Property(e => e.Wysokosc).HasColumnName("wysokosc").HasMaxLength(25);
            entity.Property(e => e.JednostkaWagi).HasColumnName("jednostka_wagi").HasMaxLength(10);
            entity.Property(e => e.IloscSztPalecie).HasColumnName("ilosc_szt_palecie");
            entity.Property(e => e.IloscDniPrzydatnosci).HasColumnName("ilosc_dni_przydatnosci");
            entity.Property(e => e.WymaganaPartia).HasColumnName("wymagana_partia");
            entity.Property(e => e.WymaganaDataWaznosci).HasColumnName("wymagana_data_waznosci");
            entity.Property(e => e.WymaganaDataprod).HasColumnName("wymagana_dataprod");
            entity.Property(e => e.StatusJakosciDomyslny).HasColumnName("status_jakosci_domyslny");
            entity.Property(e => e.DlugoscSztMm).HasColumnName("dlugosc_szt_mm");
            entity.Property(e => e.SzerokoscSztMm).HasColumnName("szerokosc_szt_mm");
            entity.Property(e => e.WysokoscSztMm).HasColumnName("wysokosc_szt_mm");
            entity.Property(e => e.JednostkaSkladowaniaDomyslna).HasColumnName("jednostka_skladowania_domyslna");
            entity.Property(e => e.KrajPochodzeniaId).HasColumnName("kraj_pochodzenia_id");
            entity.Property(e => e.NowyKodMail).HasColumnName("nowy_kod_mail");
            entity.Property(e => e.TsCreated).HasColumnName("ts_created");
            entity.Property(e => e.AdrTowary).HasColumnName("adr_towary");
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);

            // Ważne: ignorujemy nawigację do AwizacjaDane (legacy nie ma FK do kody)
            entity.Ignore(e => e.AwizacjaDane);
            
            // Indexes
            entity.HasIndex(e => new { e.KodValue, e.SystemId }).IsUnique();
            entity.HasIndex(e => e.KodValue);
            entity.HasIndex(e => e.SystemId);
            entity.HasIndex(e => e.Active);
            entity.HasIndex(e => e.IloscWOpakowaniu);
        });

        // TypyPalet -> typypalet table
        modelBuilder.Entity<TypyPalet>(entity =>
        {
            entity.ToTable("typypalet");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Kod).HasColumnName("kod").HasMaxLength(45);
            entity.Property(e => e.Opis).HasColumnName("opis").HasMaxLength(45);
            entity.Property(e => e.UdzialSkladowania).HasColumnName("udzial_skladowania").HasPrecision(4, 2);
            entity.Property(e => e.KolejnoscPal).HasColumnName("kolejnosc_pal");
            entity.Property(e => e.PalDlugosc).HasColumnName("pal_dlugosc").HasMaxLength(45);
            entity.Property(e => e.PalSzerokosc).HasColumnName("pal_szerokosc").HasMaxLength(45);
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            
            // Indexes
            entity.HasIndex(e => e.Opis).IsUnique();
        });
        
        // System -> systemy table
        modelBuilder.Entity<Wms.Domain.Entities.System>(entity =>
        {
            entity.ToTable("systemy");
            // W legacy tabeli 'systemy' nie ma kolumny 'id' — kluczem głównym jest kolumna 'wartosc'
            entity.HasKey(e => e.Wartosc);
            entity.Property(e => e.Wartosc).HasColumnName("wartosc");
            entity.Property(e => e.Nazwa).HasColumnName("nazwa").HasMaxLength(255);

            // W tej tabeli nie mapujemy właściwości Id (nie istnieje odpowiadająca kolumna)
            entity.Ignore(e => e.Id);
            
            // Ignore BaseEntity properties for existing table
            entity.Ignore(e => e.CreatedAt);
            entity.Ignore(e => e.UpdatedAt);
            entity.Ignore(e => e.CreatedBy);
            entity.Ignore(e => e.UpdatedBy);
            
            // Indexes
            entity.HasIndex(e => e.Wartosc).IsUnique();
        });
    }
}
