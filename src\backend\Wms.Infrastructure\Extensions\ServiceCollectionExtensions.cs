using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Wms.Application.Interfaces;
using Wms.Infrastructure.Data;
using Wms.Infrastructure.Interceptors;
using Wms.Infrastructure.Repositories;

namespace Wms.Infrastructure.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Add database performance logging interceptor
        services.AddScoped<PerformanceLoggingInterceptor>();
        
        // Database Context
        services.AddDbContext<WmsDbContext>((serviceProvider, options) =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            var serverVersion = ServerVersion.AutoDetect(connectionString!);
            
            options.UseMySql(connectionString, serverVersion, mysqlOptions =>
            {
                // Retry strategy - kompatybilna z execution strategy pattern
                mysqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(5),
                    errorNumbersToAdd: null);
            });
            
            // Add performance logging interceptor
            options.AddInterceptors(serviceProvider.GetRequiredService<PerformanceLoggingInterceptor>());
            
            // Enable detailed logging in development
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        });

        // Repositories
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<ISessionRepository, SessionRepository>();
        services.AddScoped<ILocationRepository, LocationRepository>();
        services.AddScoped<ILabelRepository, LabelRepository>();
        services.AddScoped<IMovementRepository, MovementRepository>();
        services.AddScoped<IReceiveRepository, ReceiveRepository>();
        services.AddScoped<IPalletRepository, PalletRepository>();
        services.AddScoped<IKodRepository, KodRepository>();
        
        // Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        
        return services;
    }
}
