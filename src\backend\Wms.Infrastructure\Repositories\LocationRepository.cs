using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class LocationRepository : ILocationRepository
{
    private readonly WmsDbContext _context;

    public LocationRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<Location?> GetByCodeAsync(string locationCode)
    {
        if (string.IsNullOrWhiteSpace(locationCode))
            return null;
            
        // Parsowanie kodu MP-H-R-M-P
        var parts = locationCode.Split('-');
        if (parts.Length != 5 || parts[0] != "MP")
            return null;

        if (!int.TryParse(parts[1], out var hala) || !int.TryParse(parts[3], out var miejsce))
            return null;

        var regal = parts[2];
        var poziom = parts[4];

        return await GetByCoordinatesAsync(hala, regal, miejsce, poziom);
    }

    public async Task<Location?> GetByCoordinatesAsync(int hala, string regal, int miejsce, string? poziom)
    {
        return await _context.Locations
            .FirstOrDefaultAsync(l => 
                l.Hala == hala && 
                l.Regal == regal && 
                l.Miejsce == miejsce && 
                l.Poziom == poziom);
    }

    public async Task<Location?> GetByIdAsync(int id)
    {
        return await _context.Locations
            .FirstOrDefaultAsync(l => l.Id == id);
    }

    public async Task<IEnumerable<Location>> GetVisibleLocationsAsync()
    {
        return await _context.Locations
            .Where(l => l.Widoczne == 1)
            .OrderBy(l => l.Hala)
            .ThenBy(l => l.Regal)
            .ThenBy(l => l.Miejsce)
            .ThenBy(l => l.Poziom)
            .ToListAsync();
    }

    public async Task<int> GetCurrentPalletCountAsync(int locationId)
    {
        // Liczba aktywnych etykiet w danej lokalizacji
        return await _context.Labels
            .Where(l => l.Miejscep == locationId && l.Active == 1)
            .Select(l => l.PaletaId)
            .Distinct()
            .CountAsync();
    }
}
