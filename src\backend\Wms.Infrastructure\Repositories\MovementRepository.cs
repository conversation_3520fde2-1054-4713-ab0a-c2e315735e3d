using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class MovementRepository : IMovementRepository
{
    private readonly WmsDbContext _context;

    public MovementRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<Movement> CreateAsync(Movement movement)
    {
        _context.Movements.Add(movement);
        // Zapis nastąpi na koń<PERSON> transakcji (UnitOfWork.SaveChangesAsync)
        await Task.CompletedTask;
        return movement;
    }

    public async Task<IEnumerable<Movement>> GetByLabelIdAsync(int labelId)
    {
        return await _context.Movements
            .Include(m => m.User)
            .Include(m => m.FromLocation)
            .Include(m => m.ToLocation)
            .Include(m => m.Label)
            .Where(m => m.Etykieta == labelId)
            .OrderByDescending(m => m.Tszm)
            .ToListAsync();
    }

    public async Task<Movement?> GetLastMovementForLabelAsync(int labelId)
    {
        return await _context.Movements
            .Where(m => m.Etykieta == labelId)
            .OrderByDescending(m => m.Tszm)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<Movement>> GetByUserIdAsync(int userId, DateTime? fromDate = null)
    {
        var query = _context.Movements
            .Include(m => m.Label)
            .Include(m => m.FromLocation)
            .Include(m => m.ToLocation)
            .Where(m => m.PracownikId == userId);

        if (fromDate.HasValue)
        {
            var dateOnly = DateOnly.FromDateTime(fromDate.Value);
            query = query.Where(m => m.Data >= dateOnly);
        }

        return await query
            .OrderByDescending(m => m.Tszm)
            .ToListAsync();
    }

    public async Task<IEnumerable<Movement>> GetRecentMovementsAsync(int limit = 100)
    {
        return await _context.Movements
            .Include(m => m.User)
            .Include(m => m.Label)
            .Include(m => m.FromLocation)
            .Include(m => m.ToLocation)
            .OrderByDescending(m => m.Tszm)
            .Take(limit)
            .ToListAsync();
    }
}
