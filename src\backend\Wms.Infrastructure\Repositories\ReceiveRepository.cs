using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities.Receives;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class ReceiveRepository : IReceiveRepository
{
    private readonly WmsDbContext _context;

    public ReceiveRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<ListControl>> GetAvailableReceivesAsync(int? warehouseId = null, bool includeAssigned = false, CancellationToken cancellationToken = default)
    {
        var query = _context.ListControls.AsQueryable();

        // Filtruj tylko dostępne dostawy (bez przypisanego realizującego pracownika) lub wszystkie jeśli includeAssigned = true
        if (!includeAssigned)
        {
            query = query.Where(lc => lc.RealizujacyPracownikId == null);
        }

        // Filtruj po magazynie jeśli podano
        if (warehouseId.HasValue)
        {
            query = query.Where(lc => lc.MiejsceId == warehouseId.Value);
        }

        // Dołącz podstawowe informacje o pracy i miejscu dostawy
        query = query
            .Include(lc => lc.RealizujacyPracownik)
            .Include(lc => lc.Pracownik)
            .Include(lc => lc.MiejsceDostawy)
            .Include(lc => lc.SystemEntity)
            .OrderByDescending(lc => lc.Id);

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<ListControl?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.ListControls
            .FirstOrDefaultAsync(lc => lc.Id == id, cancellationToken);
    }

    public async Task<ListControl?> GetWithDetailsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.ListControls
            .Include(lc => lc.RealizujacyPracownik)
            .Include(lc => lc.Pracownik)
            .Include(lc => lc.MiejsceDostawy)
            .Include(lc => lc.SystemEntity)
            .Include(lc => lc.ListControlPallets)
            .FirstOrDefaultAsync(lc => lc.Id == id, cancellationToken);
    }

    public async Task<bool> ClaimReceiveAsync(int receiveId, int userId, CancellationToken cancellationToken = default)
    {
        var receive = await _context.ListControls
            .FirstOrDefaultAsync(lc => lc.Id == receiveId, cancellationToken);

        if (receive == null)
            return false;

        // Sprawdź czy dostawa nie jest już przypisana
        if (receive.RealizujacyPracownikId != null)
            return false;

        // Przypisz pracownika
        receive.RealizujacyPracownikId = userId;
        
        // Zwróć sukces - SaveChanges będzie wywołane przez UnitOfWork w handlerze
        return true;
    }

    public async Task<bool> ReleaseReceiveAsync(int receiveId, int userId, CancellationToken cancellationToken = default)
    {
        var receive = await _context.ListControls
            .FirstOrDefaultAsync(lc => lc.Id == receiveId, cancellationToken);

        if (receive == null)
            return false;

        // Sprawdź czy dostawa jest przypisana do tego użytkownika
        if (receive.RealizujacyPracownikId != userId)
            return false;

        // Zwolnij przypisanie
        receive.RealizujacyPracownikId = null;
        
        // Zwróć sukces - SaveChanges będzie wywołane przez UnitOfWork w handlerze
        return true;
    }

    public async Task<bool> IsReceiveClaimedByUserAsync(int receiveId, int userId, CancellationToken cancellationToken = default)
    {
        var receive = await _context.ListControls
            .AsNoTracking()
            .FirstOrDefaultAsync(lc => lc.Id == receiveId, cancellationToken);

        return receive?.RealizujacyPracownikId == userId;
    }

    public async Task<IEnumerable<AwizacjaDane>> GetExpectedItemsAsync(int receiveId, CancellationToken cancellationToken = default)
    {
        // Znajdź awizację powiązaną z dostawą
        var awizacjaId = await _context.ListControls
            .Where(lc => lc.Id == receiveId)
            .Select(lc => lc.AwizacjeId)
            .FirstOrDefaultAsync(cancellationToken);

        if (awizacjaId == 0)
            return Enumerable.Empty<AwizacjaDane>();

        // Pobierz pozycje awizacji (bez żadnych relacji do 'kody' – relacja legacy nie istnieje)
        return await _context.AwizacjaDanes
            .Where(ad => ad.AwizacjeDostawId == awizacjaId)
            .Include(ad => ad.AwizacjaHead)
            .OrderBy(ad => ad.PozycjaZamowienia)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<string, (int KodId, string KodNazwa, string? Ean)>> GetKodLookupAsync(
        int systemId, IEnumerable<string> codes, CancellationToken cancellationToken = default)
    {
        var codeList = codes
            .Where(c => !string.IsNullOrWhiteSpace(c))
            .Select(c => c.Trim())
            .Distinct()
            .ToList();

        if (codeList.Count == 0)
            return new Dictionary<string, (int, string, string?)>();

        var results = await _context.Kody
            .Where(k => k.SystemId == systemId && codeList.Contains(k.KodValue))
            .Select(k => new { k.KodValue, k.Id, k.KodNazwa, k.Ean })
            .ToListAsync(cancellationToken);

        return results.ToDictionary(
            x => x.KodValue,
            x => (x.Id, x.KodNazwa, x.Ean)
        );
    }

    public async Task UpdateAsync(ListControl receive, CancellationToken cancellationToken = default)
    {
        _context.ListControls.Update(receive);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
