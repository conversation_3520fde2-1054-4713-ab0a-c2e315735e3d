using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class SessionRepository : ISessionRepository
{
    private readonly WmsDbContext _context;

    public SessionRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<Session> CreateAsync(Session session)
    {
        _context.Sessions.Add(session);
        await _context.SaveChangesAsync();
        return session;
    }

    public async Task<Session?> GetByTokenIdAsync(string tokenId)
    {
        return await _context.Sessions
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.JwtTokenId == tokenId && s.IsActive);
    }

    public async Task<IEnumerable<Session>> GetActiveSessionsAsync(int userId)
    {
        return await _context.Sessions
            .Where(s => s.UserId == userId && s.IsActive)
            .ToListAsync();
    }

    public async Task DeactivateSessionsAsync(int userId, string? deviceId = null)
    {
        var query = _context.Sessions
            .Where(s => s.UserId == userId && s.IsActive);

        if (!string.IsNullOrEmpty(deviceId))
        {
            query = query.Where(s => s.DeviceId == deviceId);
        }

        var sessions = await query.ToListAsync();

        foreach (var session in sessions)
        {
            session.IsActive = false;
            session.LogoutTime = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
    }

    public async Task SaveChangesAsync()
    {
        await _context.SaveChangesAsync();
    }
}
