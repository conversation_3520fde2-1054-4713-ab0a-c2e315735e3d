<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="WmsApp.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views="clr-namespace:WmsApp.Views"
    xmlns:receives="clr-namespace:WmsApp.Views.Receives"
    Title="WmsApp"
    FlyoutBehavior="Disabled">

    <!-- Login Page -->
    <ShellContent
        Title="Login"
        ContentTemplate="{DataTemplate views:LoginPage}"
        Route="login" />

    <!-- Main Dashboard -->
    <ShellContent
        Title="Dashboard"
        ContentTemplate="{DataTemplate views:MainPage}"
        Route="main" />

    <!-- Locations Page -->
    <ShellContent
        Title="Locations"
        ContentTemplate="{DataTemplate views:LocationsPage}"
        Route="locations" />

    <!-- Pallets Page -->
    <ShellContent
        Title="Pallets"
        ContentTemplate="{DataTemplate views:PalletsPage}"
        Route="pallets" />

    <!-- Options Page -->
    <ShellContent
        Title="Opcje"
        ContentTemplate="{DataTemplate views:OptionsPage}"
        Route="options" />

    <!-- Move Pallet Page -->
    <ShellContent
        Title="Zmiana miejsca"
        ContentTemplate="{DataTemplate views:MovePalletPage}"
        Route="movepalletpage" />

    <!-- About Page -->
    <ShellContent
        Title="O aplikacji"
        ContentTemplate="{DataTemplate views:AboutPage}"
        Route="aboutpage" />

    <!-- Mock Scanner Page (tylko dla emulatorów) -->
    <ShellContent
        Title="🖥️ Mock Scanner"
        ContentTemplate="{DataTemplate views:EmulatorScannerPage}"
        Route="emulator/scanner" />

    <!-- Receives (Przyjęcia) Section -->
    <ShellContent
        Title="Wybór dostawy"
        ContentTemplate="{DataTemplate receives:ReceivesSelectionPage}"
        Route="receivesselection" />
        
    <ShellContent
        Title="Rejestracja dostaw"
        ContentTemplate="{DataTemplate receives:ReceivesRegistrationPage}"
        Route="receivesregistration" />

</Shell>
