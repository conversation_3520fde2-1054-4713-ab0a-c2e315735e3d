using System.Collections;
using System.Globalization;
using Microsoft.Maui.Controls;
using WmsApp.Models.Receives;

namespace WmsApp.Converters;

/// <summary>
/// Konwertuje bool (IsOccupied) na tekst "Zajęte"/"<PERSON>st<PERSON><PERSON><PERSON>"
/// </summary>
public class BoolToOccupiedConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isOccupied)
        {
            return isOccupied ? "Zajęte" : "Dost<PERSON><PERSON><PERSON>";
        }
        return "Nieznane";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Konwertuje bool (IsOccupied) na kolor tekstu
/// </summary>
public class BoolToOccupiedColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isOccupied)
        {
            return isOccupied ? Colors.Red : Colors.Green;
        }
        return Colors.Gray;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Konwertuje string na bool (true jeśli nie null i nie pusty) - wersja dla modułu dostaw
/// </summary>
public class ReceiveStringToBoolConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        return !string.IsNullOrWhiteSpace(value as string);
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Konwertuje bool na odwrotną wartość
/// </summary>
public class InverseBoolConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return !boolValue;
        }
        return true;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return !boolValue;
        }
        return false;
    }
}

/// <summary>
/// Konwertuje bool na kolor (używany dla toggle buttons) - wersja dla modułu dostaw
/// </summary>
public class ReceiveBoolToColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Colors.Green : Colors.Gray;
        }
        return Colors.Gray;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Konwertuje decimal na bool (true jeśli > 0)
/// </summary>
public class DecimalToBoolConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is decimal decimalValue)
        {
            return decimalValue > 0;
        }
        return false;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Sprawdza czy string równa się parametrowi
/// </summary>
public class StringEqualsConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is string stringValue && parameter is string paramValue)
        {
            return stringValue.Equals(paramValue, StringComparison.OrdinalIgnoreCase);
        }
        return false;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Konwertuje kolekcję na bool (true jeśli nie null i nie pusta)
/// </summary>
public class CollectionToBoolConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is ICollection collection)
        {
            return collection.Count > 0;
        }
        if (value is IEnumerable enumerable)
        {
            return enumerable.Cast<object>().Any();
        }
        return false;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Konwertuje ID typu palety na obiekt TypPaletyDto (dla Picker)
/// Wymaga przekazania kolekcji typów palet w ConverterParameter
/// </summary>
public class IdToTypPaletyConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is int typPaletyId && parameter is IEnumerable<TypPaletyDto> typy)
        {
            return typy.FirstOrDefault(t => t.Id == typPaletyId);
        }
        return null;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is TypPaletyDto typ)
        {
            return typ.Id;
        }
        return 0;
    }
}

/// <summary>
/// Konwerter dla DatePicker - obsługuje nullable DateTime
/// </summary>
public class NullableDateTimeConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is DateTime dateTime)
        {
            return dateTime;
        }
        return DateTime.Today;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is DateTime dateTime)
        {
            return dateTime == DateTime.Today ? (DateTime?)null : dateTime;
        }
        return null;
    }
}

/// <summary>
/// Formatuje liczbę z odpowiednią jednostką
/// </summary>
public class NumberWithUnitConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is decimal number && parameter is string unit)
        {
            if (number == 0) return string.Empty;
            return $"{number:F2} {unit}";
        }
        return string.Empty;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Konwertuje status rejestracji na kolor
/// </summary>
public class RegistrationStatusToColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is string status)
        {
            return status.ToLower() switch
            {
                "success" or "sukces" => Colors.Green,
                "warning" or "ostrzeżenie" => Colors.Orange,
                "error" or "błąd" => Colors.Red,
                _ => Colors.Gray
            };
        }
        return Colors.Gray;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Formatuje SSCC dla wyświetlania (skraca długie kody)
/// </summary>
public class SsccDisplayConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is string sscc && sscc.Length > 8)
        {
            return $"...{sscc.Substring(sscc.Length - 6)}";
        }
        return value?.ToString() ?? string.Empty;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
