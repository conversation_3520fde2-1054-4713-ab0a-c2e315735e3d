using Microsoft.Extensions.Logging;
using CommunityToolkit.Maui;
using Refit;
using System.Globalization;
using System.Resources;
using System.Text.Json;
using WmsApp.Services;
using WmsApp.Services.Contracts;
using WmsApp.Services.Receives;
using WmsApp.Models;
using WmsApp.Views;
using WmsApp.Views.Receives;
using WmsApp.ViewModels;
using WmsApp.ViewModels.Receives;

namespace WmsApp;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseMauiCommunityToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
            });

        // Localization init (default: pl)
        var lang = Preferences.Default.Get("AppLanguage", "pl");
        var culture = CultureInfo.GetCultureInfo(lang);
        CultureInfo.DefaultThreadCurrentUICulture = culture;
        CultureInfo.DefaultThreadCurrentCulture = culture;
        var resManager = new ResourceManager("WmsApp.Resources.Strings.AppResources", typeof(MauiProgram).Assembly);
        WmsApp.Localization.LocalizationResourceManager.Current.Init(resManager);
        WmsApp.Localization.LocalizationResourceManager.Current.SetCulture(culture);

        // Register services
        ConfigureServices(builder.Services);

#if DEBUG
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // Core services
        services.AddSingleton<IPreferences>(Preferences.Default);
        services.AddSingleton<IConnectivity>(Connectivity.Current);

        // Localization service
        services.AddSingleton<ILocalizationService, LocalizationService>();
        
        // Device Detection Service (platform-specific) - moved earlier
#if ANDROID
        services.AddSingleton<IDeviceDetectionService, Platforms.Android.DeviceDetectionService>();
#else
        services.AddSingleton<IDeviceDetectionService, DeviceDetectionService>();
#endif
        
        // Authentication Service
        services.AddSingleton<IAuthService, AuthService>();
        
        // API Configuration Service  
        services.AddSingleton<IApiConfigurationService, ApiConfigurationService>();
        
        // Authorization Handler
        services.AddTransient<AuthorizationMessageHandler>();
        
        // WMS API Service - singleton to share same HttpClient instance
        services.AddSingleton<IWmsApiService>(provider =>
        {
            var apiConfig = provider.GetRequiredService<IApiConfigurationService>();
            return apiConfig.CreateApiService();
        });
        
        // Other services
        services.AddTransient<CodeValidationService>();
        
        // Product Picker Service
        services.AddSingleton<IProductPickerService, ProductPickerService>();
        
        // Mock Scanner Service
        services.AddTransient<IMockScannerService, MockScannerService>();
        
        // Receives module services
        services.AddTransient<ReceiveCodeValidationService>();
        
        // Configure ReceiveService based on API environment
        services.AddTransient<IReceiveService>(provider =>
        {
            var apiConfig = provider.GetRequiredService<IApiConfigurationService>();
            
            // Use MockReceiveService for Mock environment, real API for others
            if (apiConfig.CurrentEnvironment == ApiEnvironment.Mock)
            {
                return provider.GetRequiredService<MockReceiveService>();
            }
            else
            {
                return provider.GetRequiredService<ReceiveService>();
            }
        });
        
        // Register both implementations
        services.AddTransient<MockReceiveService>();
        services.AddTransient<ReceiveService>();
        
        // Add IReceiveApiClient for real API (only used when not Mock)
        services.AddSingleton<IReceiveApiClient>(provider =>
        {
            var apiConfig = provider.GetRequiredService<IApiConfigurationService>();
            var authService = provider.GetRequiredService<IAuthService>();
            
            var baseUrl = apiConfig.CurrentEnvironment.GetBaseUrl();
            
            // Tworzymy HttpClient z custom handler (podobnie jak w ApiConfigurationService)
            var authHandler = new AuthorizationMessageHandler(authService)
            {
                InnerHandler = new HttpClientHandler()
            };
            
            var httpClient = new HttpClient(authHandler)
            {
                BaseAddress = new Uri(baseUrl),
                Timeout = TimeSpan.FromSeconds(60)
            };
            
            httpClient.DefaultRequestHeaders.Add("User-Agent", "WmsApp/1.0 (Receives)");
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            
            var refitSettings = new RefitSettings
            {
                ContentSerializer = new SystemTextJsonContentSerializer(new System.Text.Json.JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                })
            };
            
            return RestService.For<IReceiveApiClient>(httpClient, refitSettings);
        });
        
        // Update Service with HttpClient
        services.AddHttpClient<IUpdateService, UpdateService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(60); // Longer timeout for downloads
        });
        
        // ViewModels
        services.AddTransient<LoginViewModel>();
        services.AddTransient<MainViewModel>();
        services.AddTransient<LocationsViewModel>();
        services.AddTransient<PalletsViewModel>();
        services.AddTransient<OptionsViewModel>();
        services.AddTransient<MovePalletViewModel>();
        services.AddTransient<AboutViewModel>();
        
        // Receives ViewModels
        services.AddTransient<ReceivesSelectionViewModel>();
        services.AddTransient<ReceivesRegistrationViewModel>();
        services.AddTransient<ProductPickerViewModel>();
        
        // Emulator ViewModels
        services.AddTransient<EmulatorScannerViewModel>();
        
        // Views
        services.AddTransient<LoginPage>();
        services.AddTransient<MainPage>();
        services.AddTransient<LocationsPage>();
        services.AddTransient<PalletsPage>();
        services.AddTransient<OptionsPage>();
        services.AddTransient<MovePalletPage>();
        services.AddTransient<AboutPage>();
        
        // Receives Views
        services.AddTransient<ReceivesSelectionPage>();
        services.AddTransient<ReceivesRegistrationPage>();
        services.AddTransient<ProductPickerPage>();
        
        // Emulator Views
        services.AddTransient<EmulatorScannerPage>();
        
        // Shell
        services.AddTransient<AppShell>();
    }
}
