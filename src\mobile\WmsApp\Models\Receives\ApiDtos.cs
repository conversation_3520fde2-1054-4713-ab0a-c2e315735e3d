namespace WmsApp.Models.Receives.Api;

public class ReceiveApiDto
{
    public int Id { get; set; }
    public string Lk { get; set; } = string.Empty;
    public string DokumentDostawy { get; set; } = string.Empty;
    public string SystemNazwa { get; set; } = string.Empty;
    public string MiejsceDostawy { get; set; } = string.Empty;
    public DateTime Data { get; set; }
    public bool IsAssigned { get; set; }
    public int? RealizujacyPracownikId { get; set; }
}

public class ReceiveListResponseApi
{
    public List<ReceiveApiDto> Receives { get; set; } = new();
    public int TotalCount { get; set; }
}

public class PrinterApiDto
{
    public int Id { get; set; }
    public string <PERSON>zwa { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string Lokalizacja { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class ClaimReceiveResponseApi
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime ClaimedAt { get; set; }
}

