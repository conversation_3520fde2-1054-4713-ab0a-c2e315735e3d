namespace WmsApp.Models.Receives;

// DTOs dla listy dostaw
public record ReceiveDto
{
    public int Id { get; init; }
    public string LK { get; init; } = null!;
    public string NumerZamowienia { get; init; } = null!;
    public string SystemNazwa { get; init; } = null!;
    public string MiejsceDostawy { get; init; } = null!;
    public DateTime Data { get; init; }
    public bool IsOccupied { get; init; }
    public string? OccupiedBy { get; init; }
}

// Requests dla selekcji dostaw
public record LoadReceivesRequest
{
    public bool OnlyAvailable { get; init; } = true;
}

public record ClaimReceiveRequest
{
    public string LK { get; init; } = null!;
    public string DeviceId { get; init; } = null!;
}

// Requests dla generowania DS
public record GenerateDsRequest
{
    public string LK { get; init; } = null!;
    public int TypPaletyId { get; init; }
    public int IloscPalet { get; init; }
    public string? DrukarkaNazwa { get; init; }
    public bool CzyDrukowac { get; init; } = false;
}

public record GeneratedDsResponse
{
    public string[] GeneratedCodes { get; init; } = Array.Empty<string>();
    public bool PrintSuccess { get; init; }
    public string? PrintMessage { get; init; }
}

// Modele dla typu palet
public record TypPaletyDto
{
    public int Id { get; init; }
    public string Nazwa { get; init; } = null!;
    public string? Opis { get; init; }
}

// Modele dla rejestracji dostaw
public record ReceiveRegistrationState
{
    public string? CurrentNosnik { get; init; } // SSCC lub DS lub "(Auto)"
    public string? LK { get; init; }
    public string? TowarKod { get; init; }
    public int? KodId { get; init; }
    public string? Partia { get; init; }
    public DateTime? DataWaznosci { get; init; }
    public DateTime DataProdukcji { get; init; } = DateTime.Today;
    public string? Certyfikat { get; init; }
    public int? StanJakosciId { get; init; }
    public int? TypPaletyId { get; init; }
    public decimal? IloscSztuk { get; init; }
    public decimal? IloscOpakowan { get; init; }
}

// Stary model - zostaje dla kompatybilności wstecznej
public record ProcessGs1ScanRequest
{
    public string Gs1Code { get; init; } = null!;
}

// Nowy model zgodny z backendem
public record ParseScanRequest
{
    public string ScanData { get; init; } = null!;
    public int? ListControlId { get; init; }
    public string? DeviceId { get; init; }
    public string? Context { get; init; }
}

// Stary model - zostaje dla kompatybilności wstecznej
public record ProcessGs1ScanResponse
{
    public string? SSCC { get; init; } // AI 00
    public string? EAN { get; init; } // AI 02 (GTIN)
    public string? Lot { get; init; } // AI 10
    public DateTime? DataWaznosci { get; init; } // AI 17
    public decimal? Ilosc { get; init; } // AI 37
    public KodDto[]? KodCandidates { get; init; } // Jeśli wiele kandydatów dla EAN
    public bool IzPrefix { get; init; } = true; // Czy wykryto prefiks "IZ" - false oznacza ostrzeżenie
    public string[] Warnings { get; init; } = Array.Empty<string>(); // Ostrzeżenia z parsowania
}

// Nowy model zgodny z backendem
public record ParseReceiveScanResponse
{
    public bool IsSuccess { get; init; }
    public string RawScanData { get; init; } = string.Empty;
    public string ScanType { get; init; } = string.Empty;
    public string? ErrorMessage { get; init; }
    
    // GS1 parsed data
    public bool HasIzPrefix { get; init; }
    public GS1ScanData? ParsedData { get; init; }
    
    // Form suggestion data
    public ReceiveFormSuggestion? FormSuggestion { get; init; }
}

/// <summary>
/// Sparsowane dane GS1 w formacie DTO
/// </summary>
public record GS1ScanData
{
    public string? SSCC { get; init; }
    public string? GTIN { get; init; }
    public string? Lot { get; init; }
    public string? ExpiryDate { get; init; } // Format: yyyy-MM-dd
    public decimal? Quantity { get; init; }
    public int ParsedFieldsCount { get; init; }
}

/// <summary>
/// Sugestie wypełnienia formularza na podstawie skanu
/// </summary>
public record ReceiveFormSuggestion
{
    public int? KodId { get; init; }           // ID kodu z bazy danych
    public string? KodValue { get; init; }     // Wartość kodu (EAN)
    public string? KodNazwa { get; init; }     // Nazwa produktu
    public string? Lot { get; init; }          // Numer partii
    public string? ExpiryDate { get; init; }   // Data ważności (yyyy-MM-dd)
    public decimal? SuggestedQuantity { get; init; }  // Ilość po przeliczeniu przez packaging unit
    public int? PackagingUnit { get; init; }  // Jednostka opakowania z bazy
    public bool IsFromAdvice { get; init; }   // Czy dane pochodzą z awizacji
    public string? WarningMessage { get; init; } // Ostrzeżenia (np. produkt wygasły)
    
    public bool HasSuggestions => KodId.HasValue || !string.IsNullOrEmpty(Lot) || !string.IsNullOrEmpty(ExpiryDate);
}

public record KodDto
{
    public int Id { get; init; }
    public string Kod { get; init; } = null!;
    public string Nazwa { get; init; } = null!;
    public decimal IloscWOpakowaniu { get; init; }
    public bool CzyWymaganaPartia { get; init; }
    public bool CzyWymaganaDataWaznosci { get; init; }
}

public record RegisterPositionRequest
{
    public string LK { get; init; } = null!;
    public string? NosnikCode { get; init; } // null dla Auto
    public int? TypPaletyId { get; init; } // dla Auto
    public int KodId { get; init; }
    public string? Partia { get; init; }
    public DateTime? DataWaznosci { get; init; }
    public DateTime DataProdukcji { get; init; }
    public string? Certyfikat { get; init; }
    public int? StanJakosciId { get; init; }
    public decimal IloscSztuk { get; init; }
    public string DeviceId { get; init; } = null!;
}

public record RegisterPositionResponse
{
    public string NosnikCode { get; init; } = null!; // Zwrócony lub utworzony DS/SSCC
    public bool WasCreated { get; init; } // true jeśli nowy nośnik został utworzony
    public string? ValidationWarning { get; init; } // Niezgodność z awizacją
    public bool RequiresConfirmation { get; init; } // czy trzeba potwierdzić warning
}

// Modele dla przeglądania pozycji nośnika
public record NosnikPositionDto
{
    public int Id { get; init; }
    public string TowarKod { get; init; } = null!;
    public string TowarNazwa { get; init; } = null!;
    public string? Partia { get; init; }
    public DateTime? DataWaznosci { get; init; }
    public DateTime DataProdukcji { get; init; }
    public decimal IloscSztuk { get; init; }
    public decimal IloscOpakowan { get; init; }
    public string? Certyfikat { get; init; }
    public DateTime DataPrzyjecia { get; init; }
}

public record GetNosnikPositionsResponse
{
    public NosnikPositionDto[] Positions { get; init; } = Array.Empty<NosnikPositionDto>();
    public decimal TotalSztuk { get; init; }
    public decimal TotalOpakowan { get; init; }
    public string[] Warnings { get; init; } = Array.Empty<string>();
}

// Modele dla zakończenia sesji
public record FinishReceiveSessionRequest
{
    public string LK { get; init; } = null!;
    public string DeviceId { get; init; } = null!;
}

public record FinishReceiveSessionResponse
{
    public bool CanClose { get; init; }
    public string? ClosureMessage { get; init; }
    public string[] Warnings { get; init; } = Array.Empty<string>();
}

// Modele dla awizacji (ręczny wybór towaru)
public record AwizacjaPositionDto
{
    public int Id { get; init; }
    public int SystemId { get; init; }
    public string TowarKod { get; init; } = null!;
    public string TowarNazwa { get; init; } = null!;
    public decimal IloscAwizowana { get; init; }
    public decimal IloscPrzyjeta { get; init; }
    public decimal IloscPozostala => IloscAwizowana - IloscPrzyjeta;
    public string? SSCC { get; init; }
}

// Modele requestów dla nowych endpointów backendu

/// <summary>
/// Request model dla tworzenia nośnika (zgodny z DeliveryPalletsController)
/// </summary>
public record CreateCarrierRequest
{
    public int TypPaletyId { get; init; }
    public bool Drukowac { get; init; }
    public string? DrukarkaIp { get; init; }
}

/// <summary>
/// Request model dla tworzenia pozycji (zgodny z DeliveryPalletsController)
/// </summary>
public record CreateReceiveItemRequest
{
    public int PaletaId { get; init; }
    public int KodId { get; init; }
    public string? Lot { get; init; }
    public DateOnly? DataProd { get; init; }
    public DateOnly? DataWaznosci { get; init; }
    public decimal Ilosc { get; init; }
    public string? Sscc { get; init; }
    public string? Certyfikat { get; init; }
}

/// <summary>
/// Odpowiedź z tworzenia nośnika (zgodna z CarrierDto z backendu)
/// </summary>
public record CarrierDto
{
    public int PaletaId { get; init; }
    public string Kod { get; init; } = null!;
    public string TypPaletyNazwa { get; init; } = null!;
    public DateTime DataUtworzenia { get; init; }
    public bool Drukowac { get; init; }
    public string? DrukarkaIp { get; init; }
}

/// <summary>
/// Odpowiedź z tworzenia pozycji (zgodna z ReceiveItemDto z backendu)
/// </summary>
public record ReceiveItemDto
{
    public int Id { get; init; }
    public int PaletaId { get; init; }
    public int KodId { get; init; }
    public string KodValue { get; init; } = null!;
    public string? Lot { get; init; }
    public DateOnly? DataProd { get; init; }
    public DateOnly? DataWaznosci { get; init; }
    public decimal Ilosc { get; init; }
    public string? Sscc { get; init; }
    public string? Certyfikat { get; init; }
    public DateTime DataUtworzenia { get; init; }
}

// Wspólne modele błędów
public record ApiValidationError
{
    public string Field { get; init; } = null!;
    public string Message { get; init; } = null!;
}

public record ClaimConflictError
{
    public string LK { get; init; } = null!;
    public string OccupiedBy { get; init; } = null!;
    public DateTime OccupiedSince { get; init; }
}

// Enums dla rozszerzenia ScanCodeType
public enum ReceiveScanCodeType
{
    LK,             // LK + liczba
    GS1,            // GS1-128 z AI
    PrinterIP,      // IP172.7.1.44 format
    SSCC,           // 18 cyfr
    DS,             // DS + 8 cyfr
    Unknown
}

public record ReceiveScanResult
{
    public string Code { get; init; } = null!;
    public ReceiveScanCodeType Type { get; init; }
    public bool IsValid { get; init; }
    public string? ErrorMessage { get; init; }
    public DateTime ScanTime { get; init; } = DateTime.Now;
    public string? CleanedCode { get; init; } // Np. IP usunięte z kodu IP
}
