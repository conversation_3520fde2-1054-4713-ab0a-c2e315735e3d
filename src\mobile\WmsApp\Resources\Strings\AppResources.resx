<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Login Page -->
  <data name="Login_Title" xml:space="preserve">
    <value>Logowanie</value>
  </data>
  <data name="Login_AppName" xml:space="preserve">
    <value>WMS System</value>
  </data>
  <data name="Login_Subtitle" xml:space="preserve">
    <value>Zarządzanie magazynem</value>
  </data>
  <data name="Login_Language_Label" xml:space="preserve">
    <value>Wybierz język</value>
  </data>
  <data name="Login_Environment_Label" xml:space="preserve">
    <value>Wybierz środowisko API</value>
  </data>
  <data name="Login_Environment_Picker_Title" xml:space="preserve">
    <value>Wybierz środowisko</value>
  </data>
  <data name="Login_CardNumber_Placeholder" xml:space="preserve">
    <value>Numer karty</value>
  </data>
  <data name="Login_ScanCard_Button" xml:space="preserve">
    <value>📷 Zeskanuj kartę</value>
  </data>
  <data name="Login_Login_Button" xml:space="preserve">
    <value>Zaloguj</value>
  </data>
  <data name="Login_DemoMode_Title" xml:space="preserve">
    <value>🎯 Tryb Demo</value>
  </data>
  <data name="Login_DemoMode_Subtitle" xml:space="preserve">
    <value>Spróbuj tych numerów kart:</value>
  </data>
  <data name="Login_DemoMode_Items" xml:space="preserve">
    <value>DEMO • 123456 • TEST • ADMIN</value>
  </data>
  <data name="Login_Version_Label" xml:space="preserve">
    <value>Wersja 1.0.0</value>
  </data>

  <!-- Common -->
  <data name="Common_Select" xml:space="preserve">
    <value>Wybierz</value>
  </data>
  <data name="ReceivesSelection_Title" xml:space="preserve">
    <value>Wybór dostawy</value>
  </data>
  <data name="ReceivesSelection_HeaderMain" xml:space="preserve">
    <value>PRZYJĘCIA TOWARU</value>
  </data>
  <data name="ReceivesSelection_HeaderSubtitle" xml:space="preserve">
    <value>Wybierz dostawę lub wprowadź numer LK</value>
  </data>
  <data name="ReceivesSelection_LK_Label" xml:space="preserve">
    <value>Numer LK</value>
  </data>
  <data name="ReceivesSelection_LK_Placeholder" xml:space="preserve">
    <value>Wprowadź lub zeskanuj LK (np. LK123)</value>
  </data>
  <data name="ReceivesSelection_LK_Confirm_Button" xml:space="preserve">
    <value>Potwierdź LK</value>
  </data>
  <data name="ReceivesSelection_List_Label" xml:space="preserve">
    <value>Lista dostępnych dostaw</value>
  </data>
  <data name="ReceivesSelection_Refresh_Button" xml:space="preserve">
    <value>Odśwież</value>
  </data>
  <data name="ReceivesSelection_GenerateDS_Button" xml:space="preserve">
    <value>Generuj DS</value>
  </data>
  <data name="ReceivesSelection_ListReceives_Button" xml:space="preserve">
    <value>Lista dostaw</value>
  </data>
  <data name="ReceivesSelection_GeneratedDS_Header" xml:space="preserve">
    <value>Wygenerowane kody DS:</value>
  </data>
  <data name="ReceivesSelection_ProceedToRegistration_Button" xml:space="preserve">
    <value>Przejdź do rejestracji</value>
  </data>
  <data name="Receives_GenerateDS_Title" xml:space="preserve">
    <value>Generowanie kodów DS</value>
  </data>
  <data name="Receives_GenerateDS_TypPalety_Label" xml:space="preserve">
    <value>Typ palety:</value>
  </data>
  <data name="Receives_GenerateDS_Count_Label" xml:space="preserve">
    <value>Ilość palet (1-100):</value>
  </data>
  <data name="Receives_GenerateDS_Printer_Label" xml:space="preserve">
    <value>Drukarka (opcjonalnie):</value>
  </data>
  <data name="Receives_GenerateDS_Print_Checkbox_Label" xml:space="preserve">
    <value>Drukować etykiety</value>
  </data>

  <data name="ReceivesRegistration_Title" xml:space="preserve">
    <value>Rejestracja dostaw</value>
  </data>
  <data name="ReceivesRegistration_HeaderTitle" xml:space="preserve">
    <value>REJESTRACJA POZYCJI</value>
  </data>
  <data name="ReceivesRegistration_Scan_Label" xml:space="preserve">
    <value>Skanuj kod (GS1, SSCC, DS)</value>
  </data>
  <data name="ReceivesRegistration_Scan_Placeholder" xml:space="preserve">
    <value>Zeskanuj lub wprowadź kod</value>
  </data>
  <data name="ReceivesRegistration_Scan_Button" xml:space="preserve">
    <value>Przetwórz kod</value>
  </data>
  <data name="ReceivesRegistration_ItemData_Label" xml:space="preserve">
    <value>Dane towaru</value>
  </data>
  <data name="ReceivesRegistration_Source_Awizacja_Button" xml:space="preserve">
    <value>Awizacja</value>
  </data>
  <data name="ReceivesRegistration_Source_Kartoteka_Button" xml:space="preserve">
    <value>Kartoteka</value>
  </data>
  <data name="ReceivesRegistration_Towar_Label" xml:space="preserve">
    <value>Towar:</value>
  </data>
  <data name="ReceivesRegistration_Towar_Placeholder" xml:space="preserve">
    <value>Kod towaru</value>
  </data>
  <data name="ReceivesRegistration_Search_Button" xml:space="preserve">
    <value>Szukaj</value>
  </data>
  <data name="ReceivesRegistration_Search_Placeholder" xml:space="preserve">
    <value>Wpisz kod lub nazwę do wyszukania</value>
  </data>
  <data name="ReceivesRegistration_Quantities_Opakowania_Label" xml:space="preserve">
    <value>Ilość opakowań:</value>
  </data>
  <data name="ReceivesRegistration_Quantities_Sztuk_Label" xml:space="preserve">
    <value>Ilość sztuk:</value>
  </data>
  <data name="ReceivesRegistration_Partia_Label" xml:space="preserve">
    <value>Partia/Lot:</value>
  </data>
  <data name="Common_Optional" xml:space="preserve">
    <value>Opcjonalnie</value>
  </data>
  <data name="ReceivesRegistration_DataWaznosci_Label" xml:space="preserve">
    <value>Data ważności:</value>
  </data>
  <data name="ReceivesRegistration_DataProdukcji_Label" xml:space="preserve">
    <value>Data produkcji:</value>
  </data>
  <data name="ReceivesRegistration_Certyfikat_Label" xml:space="preserve">
    <value>Certyfikat:</value>
  </data>
  <data name="ReceivesRegistration_TypPaletyForAuto_Label" xml:space="preserve">
    <value>Typ palety (dla nowego nośnika):</value>
  </data>
  <data name="ReceivesRegistration_Register_Button" xml:space="preserve">
    <value>Zarejestruj pozycję</value>
  </data>
  <data name="ReceivesRegistration_Actions_CompleteNosnik_Button" xml:space="preserve">
    <value>Nośnik kompletny</value>
  </data>
  <data name="ReceivesRegistration_Actions_Finish_Button" xml:space="preserve">
    <value>Koniec</value>
  </data>
  <data name="ReceivesRegistration_AwizacjaPositions_Header" xml:space="preserve">
    <value>Pozycje awizacji:</value>
  </data>
  <data name="ReceivesRegistration_Confirmation_Title" xml:space="preserve">
    <value>Potwierdzenie</value>
  </data>
  <data name="ReceivesRegistration_Preview_Close_Button" xml:space="preserve">
    <value>Zamknij podgląd</value>
  </data>
  <data name="ReceivesRegistration_KodSelection_Title" xml:space="preserve">
    <value>Wybierz towar</value>
  </data>

  <data name="Common_Cancel" xml:space="preserve">
    <value>Anuluj</value>
  </data>
  <data name="Common_Generate" xml:space="preserve">
    <value>Generuj</value>
  </data>
  <data name="Common_Continue" xml:space="preserve">
    <value>Kontynuuj</value>
  </data>
  <data name="Common_Preview" xml:space="preserve">
    <value>Podgląd</value>
  </data>
  <data name="Common_Close" xml:space="preserve">
    <value>Zamknij</value>
  </data>
  <data name="Common_Search" xml:space="preserve">
    <value>Szukaj</value>
  </data>
  <data name="Options_Title" xml:space="preserve">
    <value>Opcje</value>
  </data>
  <data name="Options_Header" xml:space="preserve">
    <value>Wybierz operację</value>
  </data>
  <data name="Options_MovePallet_Button" xml:space="preserve">
    <value>📦 Zmiana miejsca</value>
  </data>
  <data name="Options_SystemOptions_Header" xml:space="preserve">
    <value>Opcje systemowe</value>
  </data>
  <data name="Options_About_Button" xml:space="preserve">
    <value>ℹ️ O aplikacji</value>
  </data>

  <data name="MovePallet_Title" xml:space="preserve">
    <value>Zmiana miejsca</value>
  </data>
  <data name="MovePallet_PalletInfo_Header" xml:space="preserve">
    <value>Informacje o palecie:</value>
  </data>
  <data name="Common_ConfirmCode" xml:space="preserve">
    <value>Potwierdź kod</value>
  </data>

  <data name="Main_Title" xml:space="preserve">
    <value>WMS System</value>
  </data>
  <data name="Main_Header" xml:space="preserve">
    <value>Menu Główne</value>
  </data>
  <data name="Main_Tile_Acceptance" xml:space="preserve">
    <value>Przyjęcie</value>
  </data>
  <data name="Main_Tile_Issues" xml:space="preserve">
    <value>Wydania</value>
  </data>
  <data name="Main_Tile_Operations" xml:space="preserve">
    <value>Operacje</value>
  </data>
  <data name="Main_Tile_Tasks" xml:space="preserve">
    <value>Zadania</value>
  </data>
  <data name="Main_Tile_Inventory" xml:space="preserve">
    <value>Inwentaryzacja</value>
  </data>
  <data name="Main_Tile_Reports" xml:space="preserve">
    <value>Raporty</value>
  </data>
  <data name="Main_Tile_Movements" xml:space="preserve">
    <value>Przemieszczenia</value>
  </data>
  <data name="Main_Tile_Settings" xml:space="preserve">
    <value>Ustawienia</value>
  </data>

  <data name="Pallets_Title" xml:space="preserve">
    <value>Palety</value>
  </data>
  <data name="Pallets_Search_Placeholder" xml:space="preserve">
    <value>Szukaj palet po SSCC lub etykiecie...</value>
  </data>
  <data name="Pallets_Loading_Label" xml:space="preserve">
    <value>Ładowanie palet...</value>
  </data>
  <data name="Pallets_Empty_Title" xml:space="preserve">
    <value>Brak palet</value>
  </data>
  <data name="Pallets_Empty_Subtitle" xml:space="preserve">
    <value>Spróbuj odświeżyć lub zmienić kryteria wyszukiwania</value>
  </data>

  <data name="Locations_Title" xml:space="preserve">
    <value>Lokalizacje</value>
  </data>
  <data name="Locations_Search_Placeholder" xml:space="preserve">
    <value>Wyszukaj lokalizacje...</value>
  </data>
  <data name="Locations_Loading_Label" xml:space="preserve">
    <value>Ładowanie lokalizacji...</value>
  </data>
  <data name="Locations_Empty_Title" xml:space="preserve">
    <value>Brak lokalizacji</value>
  </data>
  <data name="Locations_Empty_Subtitle" xml:space="preserve">
    <value>Spróbuj odświeżyć lub zmienić kryteria wyszukiwania</value>
  </data>

  <data name="EmulatorScanner_Title" xml:space="preserve">
    <value>🖥️ Mock Scanner (Emulator)</value>
  </data>
  <data name="EmulatorScanner_DeviceInfo" xml:space="preserve">
    <value>Informacja o urządzeniu:</value>
  </data>
  <data name="EmulatorScanner_ManualInput_Label" xml:space="preserve">
    <value>Wpisz kod do symulacji skanu:</value>
  </data>
  <data name="EmulatorScanner_Scan_Button" xml:space="preserve">
    <value>Skanuj</value>
  </data>
  <data name="EmulatorScanner_Generate_Header" xml:space="preserve">
    <value>Wygeneruj przykładowe kody:</value>
  </data>
  <data name="EmulatorScanner_Generate_GS1" xml:space="preserve">
    <value>GS1</value>
  </data>
  <data name="EmulatorScanner_Generate_SSCC" xml:space="preserve">
    <value>SSCC</value>
  </data>
  <data name="EmulatorScanner_Generate_DS" xml:space="preserve">
    <value>DS</value>
  </data>
  <data name="EmulatorScanner_Generate_LK" xml:space="preserve">
    <value>LK</value>
  </data>
  <data name="EmulatorScanner_Generate_PrinterIP" xml:space="preserve">
    <value>IP Drukarki</value>
  </data>
  <data name="Common_Clear" xml:space="preserve">
    <value>Wyczyść</value>
  </data>
  <data name="EmulatorScanner_History_Header" xml:space="preserve">
    <value>Historia skanów:</value>
  </data>
  <data name="EmulatorScanner_ClearHistory_Button" xml:space="preserve">
    <value>Wyczyść historię</value>
  </data>
  <data name="EmulatorScanner_Instructions_Header" xml:space="preserve">
    <value>Instrukcje użytkowania:</value>
  </data>
  <data name="EmulatorScanner_Instructions_1" xml:space="preserve">
    <value>• Wprowadź kod ręcznie lub wygeneruj przykładowy</value>
  </data>
  <data name="EmulatorScanner_Instructions_2" xml:space="preserve">
    <value>• Kliknij 'Skanuj' aby zasymulować skanowanie</value>
  </data>
  <data name="EmulatorScanner_Instructions_3" xml:space="preserve">
    <value>• Wygenerowane kody są automatycznie skanowane</value>
  </data>
  <data name="EmulatorScanner_Instructions_4" xml:space="preserve">
    <value>• Ta funkcja jest dostępna tylko na emulatorach</value>
  </data>

  <data name="About_Title" xml:space="preserve">
    <value>O aplikacji</value>
  </data>
  <data name="About_Updates_Header" xml:space="preserve">
    <value>Aktualizacje</value>
  </data>
  <data name="About_CheckUpdates_Button" xml:space="preserve">
    <value>Sprawdź aktualizacje</value>
  </data>
  <data name="About_UpdateRequired_Label" xml:space="preserve">
    <value>⚠️ Wymagana aktualizacja</value>
  </data>
  <data name="About_DownloadInstall_Button" xml:space="preserve">
    <value>Pobierz i zainstaluj</value>
  </data>
</root>

