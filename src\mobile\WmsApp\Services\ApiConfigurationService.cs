using Refit;
using WmsApp.Models;
using System.Threading.Tasks;

namespace WmsApp.Services;

public interface IApiConfigurationService
{
	ApiEnvironment CurrentEnvironment { get; }
	void SetEnvironment(ApiEnvironment environment);
	IWmsApiService CreateApiService();
	void RefreshApiService(); // Odśwież service z nowym tokenem
}

public class ApiConfigurationService : IApiConfigurationService
{
	private const string EnvironmentPreferenceKey = "api_environment";
	private readonly IPreferences _preferences;
	private readonly IServiceProvider _serviceProvider;
	private readonly IAuthService _authService;
	private ApiEnvironment _currentEnvironment;

	public ApiConfigurationService(IPreferences preferences, IServiceProvider serviceProvider, IAuthService authService)
	{
		_preferences = preferences;
		_serviceProvider = serviceProvider;
		_authService = authService;

		// Determine default environment based on device type
		var defaultEnvironment = DetermineDefaultEnvironment();
		var savedEnvironment = _preferences.Get(EnvironmentPreferenceKey, (int)defaultEnvironment);
		_currentEnvironment = (ApiEnvironment)savedEnvironment;
		
		System.Diagnostics.Debug.WriteLine($"[API] Environment set to: {_currentEnvironment} ({_currentEnvironment.GetDisplayName()})");
	}

	public ApiEnvironment CurrentEnvironment => _currentEnvironment;

	public void SetEnvironment(ApiEnvironment environment)
	{
		_currentEnvironment = environment;
		_preferences.Set(EnvironmentPreferenceKey, (int)environment);
	}

	public IWmsApiService CreateApiService()
	{
		return _currentEnvironment switch
		{
			ApiEnvironment.Mock => new MockWmsApiService(),
			ApiEnvironment.DevelopmentServer => CreateRealApiService(),
			ApiEnvironment.ProductionServer => CreateRealApiService(),
			ApiEnvironment.AndroidEmulator => CreateRealApiService(),
			ApiEnvironment.AndroidHost => CreateRealApiService(),
			ApiEnvironment.TC21Proxy => CreateRealApiService(),
			ApiEnvironment.Localhost => CreateRealApiService(),
			_ => throw new ArgumentException($"Unknown environment: {_currentEnvironment}")
		};
	}

	public void RefreshApiService()
	{
		// Metoda do odświeżenia API service gdy token się zmieni
		// Implementacja zależy od tego jak chcesz zarządzać instancjami
		System.Diagnostics.Debug.WriteLine("[API] RefreshApiService called");
	}
	
	private ApiEnvironment DetermineDefaultEnvironment()
	{
		// Try to get device detection service to determine if we're on emulator
		try
		{
			var deviceDetection = _serviceProvider.GetService<IDeviceDetectionService>();
			if (deviceDetection?.IsEmulator == true)
			{
				System.Diagnostics.Debug.WriteLine("[API] Detected emulator - using AndroidEmulator environment");
				return ApiEnvironment.AndroidEmulator;
			}
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"[API] Error detecting device type: {ex.Message}");
		}
		
		// Default to Mock for development
		System.Diagnostics.Debug.WriteLine("[API] Using default Mock environment");
		return ApiEnvironment.Mock;
	}

	private IWmsApiService CreateRealApiService()
	{
		var baseUrl = _currentEnvironment.GetBaseUrl();
		System.Diagnostics.Debug.WriteLine($"[WMS] Creating API service for: {baseUrl}");

		// Tworzymy HttpClient z custom handler
		var authHandler = new AuthorizationMessageHandler(_authService)
		{
			InnerHandler = new HttpClientHandler()
		};

		var httpClient = new HttpClient(authHandler)
		{
			BaseAddress = new Uri(baseUrl),
			Timeout = TimeSpan.FromSeconds(60) // Zwiększony timeout dla urządzeń mobilnych
		};

		// Add headers for mobile devices
		httpClient.DefaultRequestHeaders.Add("User-Agent", "WmsApp/1.0 (TC21)");
		httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

		System.Diagnostics.Debug.WriteLine($"[WMS] HTTP Client configured with timeout: {httpClient.Timeout}");

		// Refit settings z debugowaniem
		var jsonOptions = new System.Text.Json.JsonSerializerOptions
		{
			PropertyNameCaseInsensitive = true,
			PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
			WriteIndented = false
		};
		
		var refitSettings = new RefitSettings
		{
			ContentSerializer = new SystemTextJsonContentSerializer(jsonOptions)
		};

		System.Diagnostics.Debug.WriteLine($"[WMS] Refit configured with JSON serializer and AuthorizationMessageHandler");

		return RestService.For<IWmsApiService>(httpClient, refitSettings);
	}
}
