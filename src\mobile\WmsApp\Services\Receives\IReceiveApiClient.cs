using Refit;
using WmsApp.Models.Receives;
using WmsApp.Models.Receives.Api;

namespace WmsApp.Services.Receives;

/// <summary>
/// API client interface dla modułu dostaw
/// Definiuje wszystkie endpointy komunikacji z backendem
/// </summary>
public interface IReceiveApiClient
{
    // ===== SELEKCJA DOSTAW =====
    
    [Get("/Receives")]
    Task<IApiResponse<ReceiveListResponseApi>> GetReceivesAsync(
        [Query] int? magazynId = null,
        [Query] int limit = 100,
        [Query] bool includeAssigned = false);
    
    [Post("/Receives/{id}/claim")]
    Task<IApiResponse<ClaimReceiveResponseApi>> ClaimReceiveAsync(int id);
    
    [Post("/Receives/{id}/release")]
    Task<IApiResponse<ClaimReceiveResponseApi>> ReleaseReceiveAsync(int id);
    
    // ===== TYPY PALET I DRUKARKI =====
    
    [Get("/lookup/typy-palet")]
    Task<IApiResponse<TypPaletyDto[]>> GetTypyPaletAsync();
    
    [Get("/Printers")]
    Task<IApiResponse<List<PrinterApiDto>>> GetPrintersAsync([Query] bool onlyActive = true);
    
    // ===== GENEROWANIE DS =====
    
    [Post("/receives/generate-ds")]
    Task<IApiResponse<GeneratedDsResponse>> GenerateDsAsync([Body] GenerateDsRequest request);
    
    // ===== PARSOWANIE GS1 =====
    
    [Post("/receives/parse-gs1")]
    Task<IApiResponse<ProcessGs1ScanResponse>> ParseGs1ScanAsync([Body] ProcessGs1ScanRequest request);
    
    // Nowy endpoint zgodny z backendem
    [Post("/receives/scan")]
    Task<IApiResponse<ParseReceiveScanResponse>> ParseReceiveScanAsync([Body] ParseScanRequest request);
    
    // ===== LOOKUP TOWARU =====
    
    [Get("/lookup/kody/{kodId}")]
    Task<IApiResponse<KodDto>> GetKodByIdAsync(int kodId);

    [Get("/lookup/kody/by-code/{kod}")]
    Task<IApiResponse<KodDto>> GetKodByCodeAsync(string kod, [Query] int? systemId = null);
    
    [Get("/lookup/kody/search")]
    Task<IApiResponse<KodDto[]>> SearchKodyAsync([Query] string query, [Query] int limit = 10);
    
    // ===== REJESTRACJA POZYCJI - STARE ENDPOINTY (zostaną usunięte) =====
    
    [Post("/receives/register-position")]
    [Obsolete("Użyj nowych endpointów: CreateCarrierAsync i CreateReceiveItemAsync")]
    Task<IApiResponse<RegisterPositionResponse>> RegisterPositionAsync([Body] RegisterPositionRequest request);
    
    [Post("/receives/register-position/confirm")]
    [Obsolete("Użyj nowych endpointów: CreateCarrierAsync i CreateReceiveItemAsync")]
    Task<IApiResponse<RegisterPositionResponse>> ConfirmRegisterPositionAsync([Body] RegisterPositionRequest request);
    
    // ===== NOWE ENDPOINTY ZGODNE Z BACKENDEM =====
    
    /// <summary>
    /// Tworzy nowy nośnik (paleta DS) dla dostawy
    /// </summary>
    [Post("/dostawy/{deliveryId}/nosnik")]
    Task<IApiResponse<CarrierDto>> CreateCarrierAsync(
        int deliveryId,
        [Body] CreateCarrierRequest request);
    
    /// <summary>
    /// Dodaje pozycję do dostawy na określonym nośniku
    /// </summary>
    [Post("/dostawy/{deliveryId}/pozycje")]
    Task<IApiResponse<ReceiveItemDto>> CreateReceiveItemAsync(
        int deliveryId,
        [Body] CreateReceiveItemRequest request);
    
    /// <summary>
    /// Pobiera listę nośników dla dostawy
    /// </summary>
    [Get("/Receives/{deliveryId}/nosniki")]
    Task<IApiResponse<List<CarrierDto>>> GetDeliveryCarriersAsync(
        int deliveryId);
    
    /// <summary>
    /// Kończy pracę z nośnikiem (oznacza jako ukończony)
    /// </summary>
    [Post("/dostawy/{deliveryId}/nosniki/{paletaId}/complete")]
    Task<IApiResponse<bool>> CompleteCarrierAsync(
        int deliveryId,
        int paletaId);
    
    // ===== ZARZĄDZANIE NOŚNIKAMI =====
    
    [Get("/nosniki/{nosnikCode}/positions")]
    Task<IApiResponse<GetNosnikPositionsResponse>> GetNosnikPositionsAsync(string nosnikCode);
    
    [Post("/nosniki/complete")]
    Task<IApiResponse<object>> CompleteNosnikAsync([Body] CompleteNosnikRequest request);
    
    // ===== AWIZACJE (ręczny wybór towaru) =====
    
    [Get("/receives/{lk}/awizacja-positions")]
    Task<IApiResponse<AwizacjaPositionDto[]>> GetAwizacjaPositionsAsync(string lk);
    
    // ===== ZAKOŃCZENIE SESJI =====
    
    [Post("/receives/finish-session")]
    Task<IApiResponse<FinishReceiveSessionResponse>> FinishReceiveSessionAsync([Body] FinishReceiveSessionRequest request);
}

// Dodatkowe modele dla API
public record CompleteNosnikRequest
{
    public string NosnikCode { get; init; } = null!;
    public string DeviceId { get; init; } = null!;
}
