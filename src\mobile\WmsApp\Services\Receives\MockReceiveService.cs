using Microsoft.Extensions.Logging;
using WmsApp.Models.Receives;

namespace WmsApp.Services.Receives;

/// <summary>
/// Mock implementacja IReceiveService dla testowania bez prawdziwego API
/// Symuluje wszystkie operacje modułu dostaw z realistycznymi danymi
/// </summary>
public class MockReceiveService : IReceiveService
{
    private readonly ILogger<MockReceiveService> _logger;
    private readonly ReceiveCodeValidationService _validationService;
    
    // Mock data storage
    private readonly List<ReceiveDto> _mockReceives = new();
    private readonly List<TypPaletyDto> _mockTypyPalet = new();
    private readonly List<string> _mockDrukarki = new();
    private readonly List<KodDto> _mockKody = new();
    private readonly List<AwizacjaPositionDto> _mockAwizacjaPositions = new();
    private readonly Dictionary<string, List<NosnikPositionDto>> _mockNosnikPositions = new();
    private readonly HashSet<string> _claimedReceives = new();

    public MockReceiveService(ILogger<MockReceiveService> logger, ReceiveCodeValidationService validationService)
    {
        _logger = logger;
        _validationService = validationService;
        InitializeMockData();
    }

    #region Mock Data Initialization

    private void InitializeMockData()
    {
        // Mock dostaw
        _mockReceives.AddRange(new[]
        {
            new ReceiveDto { Id = 1, LK = "LK001", NumerZamowienia = "ZAM/2025/001", SystemNazwa = "SAP", MiejsceDostawy = "Rampa A", Data = DateTime.Today, IsOccupied = false },
            new ReceiveDto { Id = 2, LK = "LK002", NumerZamowienia = "ZAM/2025/002", SystemNazwa = "Oracle", MiejsceDostawy = "Rampa B", Data = DateTime.Today.AddDays(-1), IsOccupied = true, OccupiedBy = "USER001" },
            new ReceiveDto { Id = 3, LK = "LK003", NumerZamowienia = "ZAM/2025/003", SystemNazwa = "SAP", MiejsceDostawy = "Rampa C", Data = DateTime.Today, IsOccupied = false },
            new ReceiveDto { Id = 4, LK = "LK123", NumerZamowienia = "TEST/001", SystemNazwa = "Test", MiejsceDostawy = "Test Area", Data = DateTime.Today, IsOccupied = false }
        });

        // Mock typów palet
        _mockTypyPalet.AddRange(new[]
        {
            new TypPaletyDto { Id = 1, Nazwa = "EUR 120x80", Opis = "Paleta europejska standardowa" },
            new TypPaletyDto { Id = 2, Nazwa = "EUR 120x100", Opis = "Paleta europejska wydłużona" },
            new TypPaletyDto { Id = 3, Nazwa = "Jednorazowa", Opis = "Paleta jednorazowa" },
            new TypPaletyDto { Id = 4, Nazwa = "Plastikowa", Opis = "Paleta z tworzywa sztucznego" }
        });

        // Mock drukarek
        _mockDrukarki.AddRange(new[]
        {
            "172.7.1.44",
            "172.7.1.45", 
            "172.7.1.46",
            "192.168.1.100"
        });

        // Mock kodów towaru
        _mockKody.AddRange(new[]
        {
            new KodDto { Id = 1, Kod = "TEST001", Nazwa = "Testowy towar 1", IloscWOpakowaniu = 24, CzyWymaganaPartia = true, CzyWymaganaDataWaznosci = false },
            new KodDto { Id = 2, Kod = "TEST002", Nazwa = "Testowy towar 2", IloscWOpakowaniu = 12, CzyWymaganaPartia = false, CzyWymaganaDataWaznosci = true },
            new KodDto { Id = 3, Kod = "MILK001", Nazwa = "Mleko UHT 1L", IloscWOpakowaniu = 12, CzyWymaganaPartia = true, CzyWymaganaDataWaznosci = true },
            new KodDto { Id = 4, Kod = "BREAD01", Nazwa = "Chleb pszenny 500g", IloscWOpakowaniu = 6, CzyWymaganaPartia = false, CzyWymaganaDataWaznosci = true }
        });

        // Mock pozycji awizacji
        _mockAwizacjaPositions.AddRange(new[]
        {
            new AwizacjaPositionDto { Id = 1, TowarKod = "TEST001", TowarNazwa = "Testowy towar 1", IloscAwizowana = 100, IloscPrzyjeta = 0, SSCC = "123456789012345678" },
            new AwizacjaPositionDto { Id = 2, TowarKod = "TEST002", TowarNazwa = "Testowy towar 2", IloscAwizowana = 50, IloscPrzyjeta = 25 },
            new AwizacjaPositionDto { Id = 3, TowarKod = "MILK001", TowarNazwa = "Mleko UHT 1L", IloscAwizowana = 240, IloscPrzyjeta = 0 }
        });

        _logger.LogInformation("Zainicjalizowano mock data dla ReceiveService");
    }

    #endregion

    #region IReceiveService Implementation

    public async Task<ReceiveDto[]> GetAvailableReceivesAsync()
    {
        await Task.Delay(500); // Symuluj opóźnienie sieci
        var available = _mockReceives.Where(r => !r.IsOccupied && !_claimedReceives.Contains(r.LK)).ToArray();
        _logger.LogInformation("Zwrócono {Count} dostępnych dostaw", available.Length);
        return available;
    }

    public async Task<bool> ClaimReceiveAsync(string lk, string deviceId)
    {
        await Task.Delay(300);
        
        var receive = _mockReceives.FirstOrDefault(r => r.LK == lk);
        if (receive == null)
        {
            _logger.LogWarning("Nie znaleziono dostawy {LK}", lk);
            return false;
        }

        if (receive.IsOccupied || _claimedReceives.Contains(lk))
        {
            _logger.LogWarning("Dostawa {LK} jest już zajęta", lk);
            return false;
        }

        _claimedReceives.Add(lk);
        _logger.LogInformation("Zaclaimowano dostawę {LK} przez {DeviceId}", lk, deviceId);
        return true;
    }

    public async Task<bool> ReleaseReceiveAsync(string lk, string deviceId)
    {
        await Task.Delay(200);
        var released = _claimedReceives.Remove(lk);
        if (released)
        {
            _logger.LogInformation("Zwolniono claim dostawy {LK} przez {DeviceId}", lk, deviceId);
        }
        return released;
    }

    public async Task<TypPaletyDto[]> GetTypyPaletAsync()
    {
        await Task.Delay(200);
        return _mockTypyPalet.ToArray();
    }

    public async Task<string[]> GetDrukarkiAsync()
    {
        await Task.Delay(200);
        return _mockDrukarki.ToArray();
    }

    public async Task<GeneratedDsResponse> GenerateDsAsync(string lk, int typPaletyId, int iloscPalet, string? drukarkaNazwa, bool czyDrukowac)
    {
        await Task.Delay(1000); // Symuluj czas generowania

        var codes = new List<string>();
        for (int i = 1; i <= iloscPalet; i++)
        {
            var dsCode = $"DS{DateTime.Now:yyyyMMdd}{i:D4}";
            codes.Add(dsCode);
        }

        var printSuccess = czyDrukowac && !string.IsNullOrEmpty(drukarkaNazwa);
        var printMessage = czyDrukowac 
            ? (printSuccess ? $"Wydrukowano na {drukarkaNazwa}" : "Błąd drukowania")
            : null;

        _logger.LogInformation("Wygenerowano {Count} kodów DS dla {LK}", codes.Count, lk);

        return new GeneratedDsResponse
        {
            GeneratedCodes = codes.ToArray(),
            PrintSuccess = printSuccess,
            PrintMessage = printMessage
        };
    }

    public async Task<ProcessGs1ScanResponse> ParseGs1ScanAsync(string gs1Code)
    {
        await Task.Delay(300);

        // Prosta symulacja parsowania GS1 - w rzeczywistości to będzie bardziej złożone
        var response = new ProcessGs1ScanResponse();

        if (gs1Code.Contains("00")) // AI 00 - SSCC
        {
            response = response with { SSCC = "123456789012345678" };
        }

        if (gs1Code.Contains("02")) // AI 02 - EAN
        {
            // Symuluj wiele kandydatów
            response = response with 
            { 
                EAN = "5901234123457",
                KodCandidates = _mockKody.Take(2).ToArray()
            };
        }

        if (gs1Code.Contains("10")) // AI 10 - Lot
        {
            response = response with { Lot = "LOT2025001" };
        }

        if (gs1Code.Contains("17")) // AI 17 - Data ważności
        {
            response = response with { DataWaznosci = DateTime.Today.AddMonths(6) };
        }

        if (gs1Code.Contains("37")) // AI 37 - Ilość
        {
            response = response with { Ilosc = 24 };
        }

        _logger.LogInformation("Sparsowano GS1 code");
        return response;
    }

    public async Task<ParseReceiveScanResponse> ParseReceiveScanAsync(string scanData, int? listControlId = null, string? deviceId = null, string context = "delivery")
    {
        await Task.Delay(300);
        
        _logger.LogInformation("Mock parsowanie skanu: {ScanData} (context: {Context})", scanData.Substring(0, Math.Min(20, scanData.Length)), context);
        
        // Symulacja parsowania GS1
        if (scanData.StartsWith("]C1") || scanData.Contains("00") || scanData.Contains("02"))
        {
            var hasIzPrefix = scanData.Contains("IZ") || Random.Shared.NextDouble() > 0.3; // 70% szans na prefiks IZ
            
            var gs1Data = new GS1ScanData
            {
                SSCC = scanData.Contains("00") ? "123456789012345678" : null,
                GTIN = scanData.Contains("02") ? "5901234123457" : null,
                Lot = scanData.Contains("10") ? "LOT2025001" : null,
                ExpiryDate = scanData.Contains("17") ? DateTime.Today.AddMonths(6).ToString("yyyy-MM-dd") : null,
                Quantity = scanData.Contains("37") ? 24 : null,
                ParsedFieldsCount = 3
            };
            
            var formSuggestion = new ReceiveFormSuggestion
            {
                KodId = gs1Data.GTIN != null ? _mockKody.FirstOrDefault()?.Id : null,
                KodValue = gs1Data.GTIN,
                KodNazwa = gs1Data.GTIN != null ? _mockKody.FirstOrDefault()?.Nazwa : null,
                Lot = gs1Data.Lot,
                ExpiryDate = gs1Data.ExpiryDate,
                SuggestedQuantity = gs1Data.Quantity,
                PackagingUnit = 24,
                IsFromAdvice = listControlId.HasValue,
                WarningMessage = !hasIzPrefix ? "UWAGA: Brak prefiksu IZ - towar może nie być z Polski" : null
            };
            
            return new ParseReceiveScanResponse
            {
                IsSuccess = true,
                RawScanData = scanData,
                ScanType = "GS1",
                HasIzPrefix = hasIzPrefix,
                ParsedData = gs1Data,
                FormSuggestion = formSuggestion
            };
        }
        
        // Symulacja innych typów kodów
        if (scanData.StartsWith("DS") && scanData.Length == 10)
        {
            return new ParseReceiveScanResponse
            {
                IsSuccess = true,
                RawScanData = scanData,
                ScanType = "DS",
                HasIzPrefix = true // DS kody są zawsze polskie
            };
        }
        
        if (scanData.Length == 18 && scanData.All(char.IsDigit))
        {
            return new ParseReceiveScanResponse
            {
                IsSuccess = true,
                RawScanData = scanData,
                ScanType = "SSCC",
                HasIzPrefix = true
            };
        }
        
        // Nierozpoznany kod
        return new ParseReceiveScanResponse
        {
            IsSuccess = false,
            RawScanData = scanData,
            ScanType = "Unknown",
            ErrorMessage = "Nierozpoznany typ kodu",
            HasIzPrefix = false
        };
    }

    public async Task<KodDto[]> SearchKodyAsync(string query, int limit = 10)
    {
        await Task.Delay(300);
        
        var results = _mockKody
            .Where(k => k.Kod.Contains(query, StringComparison.OrdinalIgnoreCase) || 
                       k.Nazwa.Contains(query, StringComparison.OrdinalIgnoreCase))
            .Take(limit)
            .ToArray();

        return results;
    }

    public async Task<KodDto> GetKodByIdAsync(int kodId)
    {
        await Task.Delay(200);
        
        var kod = _mockKody.FirstOrDefault(k => k.Id == kodId);
        if (kod == null)
        {
            throw new InvalidOperationException($"Nie znaleziono kodu o ID {kodId}");
        }

        return kod;
    }

    public async Task<KodDto> GetKodByCodeAsync(string kod, int? systemId = null)
    {
        await Task.Delay(150);
        var result = _mockKody.FirstOrDefault(k => string.Equals(k.Kod, kod, StringComparison.OrdinalIgnoreCase));
        if (result == null)
            throw new InvalidOperationException($"Nie znaleziono kodu '{kod}'");
        return result;
    }

    public async Task<RegisterPositionResponse> RegisterPositionAsync(RegisterPositionRequest request)
    {
        await Task.Delay(500);

        var nosnikCode = request.NosnikCode ?? $"DS{DateTime.Now:yyyyMMddHHmmss}";
        var wasCreated = request.NosnikCode == null;

        // Symuluj ostrzeżenie walidacyjne w 30% przypadków
        var requiresConfirmation = Random.Shared.NextDouble() < 0.3;
        var validationWarning = requiresConfirmation ? "Ilość różni się od awizacji. Czy kontynuować?" : null;

        if (!requiresConfirmation)
        {
            // Dodaj pozycję do mock storage
            if (!_mockNosnikPositions.ContainsKey(nosnikCode))
            {
                _mockNosnikPositions[nosnikCode] = new List<NosnikPositionDto>();
            }

            var kod = await GetKodByIdAsync(request.KodId);
            var position = new NosnikPositionDto
            {
                Id = Random.Shared.Next(1000, 9999),
                TowarKod = kod.Kod,
                TowarNazwa = kod.Nazwa,
                Partia = request.Partia,
                DataWaznosci = request.DataWaznosci,
                DataProdukcji = request.DataProdukcji,
                IloscSztuk = request.IloscSztuk,
                IloscOpakowan = kod.IloscWOpakowaniu > 0 ? request.IloscSztuk / kod.IloscWOpakowaniu : 0,
                Certyfikat = request.Certyfikat,
                DataPrzyjecia = DateTime.Now
            };

            _mockNosnikPositions[nosnikCode].Add(position);
        }

        _logger.LogInformation("Zarejestrowano pozycję na nośniku {NosnikCode}", nosnikCode);

        return new RegisterPositionResponse
        {
            NosnikCode = nosnikCode,
            WasCreated = wasCreated,
            ValidationWarning = validationWarning,
            RequiresConfirmation = requiresConfirmation
        };
    }

    public async Task<RegisterPositionResponse> ConfirmRegisterPositionAsync(RegisterPositionRequest request)
    {
        await Task.Delay(300);
        
        // Po potwierdzeniu po prostu wykonaj normalną rejestrację bez walidacji
        return await RegisterPositionAsync(request);
    }

    public async Task<GetNosnikPositionsResponse> GetNosnikPositionsAsync(string nosnikCode)
    {
        await Task.Delay(300);

        if (!_mockNosnikPositions.ContainsKey(nosnikCode))
        {
            return new GetNosnikPositionsResponse();
        }

        var positions = _mockNosnikPositions[nosnikCode].ToArray();
        var totalSztuk = positions.Sum(p => p.IloscSztuk);
        var totalOpakowan = positions.Sum(p => p.IloscOpakowan);

        return new GetNosnikPositionsResponse
        {
            Positions = positions,
            TotalSztuk = totalSztuk,
            TotalOpakowan = totalOpakowan,
            Warnings = positions.Length == 0 ? new[] { "Brak pozycji na nośniku" } : Array.Empty<string>()
        };
    }

    public async Task CompleteNosnikAsync(string nosnikCode, string deviceId)
    {
        await Task.Delay(300);
        _logger.LogInformation("Oznaczono nośnik {NosnikCode} jako kompletny przez {DeviceId}", nosnikCode, deviceId);
        // W rzeczywistej implementacji oznaczałoby nośnik jako zamknięty
    }

    public async Task<AwizacjaPositionDto[]> GetAwizacjaPositionsAsync(string lk)
    {
        await Task.Delay(400);
        
        // Zwróć pozycje awizacji dla danego LK (w mock wszystkie)
        return _mockAwizacjaPositions.ToArray();
    }

    public async Task<FinishReceiveSessionResponse> FinishReceiveSessionAsync(string lk, string deviceId)
    {
        await Task.Delay(500);

        // Symuluj różne scenariusze zakończenia
        var canClose = Random.Shared.NextDouble() > 0.2; // 80% szans na możliwość zamknięcia
        
        var warnings = new List<string>();
        if (!canClose)
        {
            warnings.Add("Pozostały niezarejestrowane pozycje awizacji");
            warnings.Add("Sprawdź wszystkie nośniki przed zamknięciem");
        }

        var closureMessage = canClose 
            ? "Dostawa została pomyślnie zamknięta" 
            : "Nie można zamknąć dostawy - sprawdź ostrzeżenia";

        return new FinishReceiveSessionResponse
        {
            CanClose = canClose,
            ClosureMessage = closureMessage,
            Warnings = warnings.ToArray()
        };
    }

    // ===== NOWE METODY ZGODNE Z BACKENDEM =====

    public async Task<CarrierDto> CreateCarrierAsync(int deliveryId, int typPaletyId, bool drukowac = false, string? drukarkaIp = null)
    {
        await Task.Delay(800); // Symuluj czas tworzenia nośnika
        
        var typPalety = _mockTypyPalet.FirstOrDefault(t => t.Id == typPaletyId);
        if (typPalety == null)
        {
            throw new InvalidOperationException($"Nie znaleziono typu palety o ID {typPaletyId}");
        }
        
        var dsCode = $"DS{DateTime.Now:yyyyMMddHHmmss}";
        
        _logger.LogInformation("Mock: utworzono nośnik {DsCode} dla dostawy {DeliveryId}", dsCode, deliveryId);
        
        return new CarrierDto
        {
            PaletaId = Random.Shared.Next(1000, 9999),
            Kod = dsCode,
            TypPaletyNazwa = typPalety.Nazwa,
            DataUtworzenia = DateTime.Now,
            Drukowac = drukowac,
            DrukarkaIp = drukarkaIp
        };
    }

    public async Task<ReceiveItemDto> CreateReceiveItemAsync(int deliveryId, CreateReceiveItemRequest request)
    {
        await Task.Delay(500); // Symuluj czas dodawania pozycji
        
        var kod = _mockKody.FirstOrDefault(k => k.Id == request.KodId);
        if (kod == null)
        {
            throw new InvalidOperationException($"Nie znaleziono kodu o ID {request.KodId}");
        }
        
        _logger.LogInformation("Mock: dodano pozycję dla kodu {KodId} na paletę {PaletaId}", request.KodId, request.PaletaId);
        
        return new ReceiveItemDto
        {
            Id = Random.Shared.Next(10000, 99999),
            PaletaId = request.PaletaId,
            KodId = request.KodId,
            KodValue = kod.Kod,
            Lot = request.Lot,
            DataProd = request.DataProd,
            DataWaznosci = request.DataWaznosci,
            Ilosc = request.Ilosc,
            Sscc = request.Sscc ?? $"{Random.Shared.Next(100000000, 999999999):000000000}{Random.Shared.Next(100000000, 999999999):000000000}",
            Certyfikat = request.Certyfikat,
            DataUtworzenia = DateTime.Now
        };
    }

    public async Task<CarrierDto[]> GetDeliveryCarriersAsync(int deliveryId)
    {
        await Task.Delay(300);
        
        // Symuluj kilka nośników dla dostawy
        var carriers = new List<CarrierDto>();
        
        // Dodaj 0-3 istniejące nośniki w zależności od delivery ID
        var carrierCount = deliveryId % 4; // 0, 1, 2 lub 3 nośniki
        
        for (int i = 1; i <= carrierCount; i++)
        {
            carriers.Add(new CarrierDto
            {
                PaletaId = deliveryId * 100 + i,
                Kod = $"DS{DateTime.Today:yyyyMMdd}{deliveryId:D3}{i:D2}",
                TypPaletyNazwa = _mockTypyPalet[i % _mockTypyPalet.Count].Nazwa,
                DataUtworzenia = DateTime.Now.AddHours(-i),
                Drukowac = false,
                DrukarkaIp = null
            });
        }
        
        _logger.LogInformation("Mock: zwrócono {Count} nośników dla dostawy {DeliveryId}", carriers.Count, deliveryId);
        return carriers.ToArray();
    }

    public async Task<bool> CompleteCarrierAsync(int deliveryId, int paletaId)
    {
        await Task.Delay(400);
        
        _logger.LogInformation("Mock: oznaczono nośnik {PaletaId} jako ukończony w dostawie {DeliveryId}", paletaId, deliveryId);
        
        // Symuluj 95% sukcesu
        return Random.Shared.NextDouble() > 0.05;
    }

    #endregion
}
