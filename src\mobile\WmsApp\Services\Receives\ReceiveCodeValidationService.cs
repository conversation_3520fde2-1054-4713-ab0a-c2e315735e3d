using System.Net;
using System.Text.RegularExpressions;
using WmsApp.Models.Receives;

namespace WmsApp.Services.Receives;

/// <summary>
/// Ser<PERSON>s walidacji kodów dla modułu dostaw
/// Obsługuje walidację LK, GS1, <PERSON> drukarek, SSCC, DS
/// </summary>
public class ReceiveCodeValidationService
{
    // Wzorce walidacji
    private static readonly Regex LkPattern = new(@"^LK\d+$", RegexOptions.Compiled);
    private static readonly Regex Gs1Pattern = new(@"^[\x1D]?\d{2}.*", RegexOptions.Compiled); // AI pattern
    private static readonly Regex PrinterIpPattern = new(@"^IP(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})$", RegexOptions.Compiled);
    private static readonly Regex SsccPattern = new(@"^\d{18}$", RegexOptions.Compiled);
    private static readonly Regex DsPattern = new(@"^DS\d{8}$", RegexOptions.Compiled);

    /// <summary>
    /// Parsuje i waliduje zeskanowany kod
    /// </summary>
    public ReceiveScanResult ProcessScan(string scanCode)
    {
        if (string.IsNullOrWhiteSpace(scanCode))
        {
            return new ReceiveScanResult
            {
                Code = scanCode ?? string.Empty,
                Type = ReceiveScanCodeType.Unknown,
                IsValid = false,
                ErrorMessage = "Pusty kod skanowania"
            };
        }

        var cleanCode = scanCode.Trim();

        // Sprawdź typ kodu i waliduj
        if (IsValidLK(cleanCode))
        {
            return new ReceiveScanResult
            {
                Code = cleanCode,
                Type = ReceiveScanCodeType.LK,
                IsValid = true,
                CleanedCode = ExtractLKNumber(cleanCode)
            };
        }

        if (IsValidPrinterIP(cleanCode))
        {
            var cleanedIp = ExtractIPFromPrinterCode(cleanCode);
            return new ReceiveScanResult
            {
                Code = cleanCode,
                Type = ReceiveScanCodeType.PrinterIP,
                IsValid = IsValidIPAddress(cleanedIp),
                CleanedCode = cleanedIp,
                ErrorMessage = IsValidIPAddress(cleanedIp) ? null : "Nieprawidłowy format adresu IP"
            };
        }

        if (IsValidSSCC(cleanCode))
        {
            return new ReceiveScanResult
            {
                Code = cleanCode,
                Type = ReceiveScanCodeType.SSCC,
                IsValid = true,
                CleanedCode = cleanCode
            };
        }

        if (IsValidDS(cleanCode))
        {
            return new ReceiveScanResult
            {
                Code = cleanCode,
                Type = ReceiveScanCodeType.DS,
                IsValid = true,
                CleanedCode = cleanCode
            };
        }

        if (IsValidGS1(cleanCode))
        {
            return new ReceiveScanResult
            {
                Code = cleanCode,
                Type = ReceiveScanCodeType.GS1,
                IsValid = true,
                CleanedCode = cleanCode
            };
        }

        return new ReceiveScanResult
        {
            Code = cleanCode,
            Type = ReceiveScanCodeType.Unknown,
            IsValid = false,
            ErrorMessage = "Nierozpoznany typ kodu"
        };
    }

    /// <summary>
    /// Sprawdza czy kod to prawidłowy LK (LK + liczba bez zer wiodących)
    /// </summary>
    public static bool IsValidLK(string code)
    {
        if (string.IsNullOrWhiteSpace(code)) return false;
        
        if (!LkPattern.IsMatch(code)) return false;
        
        // Sprawdź czy liczba po LK nie ma niepotrzebnych zer wiodących
        var numberPart = code[2..];
        return !numberPart.StartsWith("0") || numberPart == "0";
    }

    /// <summary>
    /// Sprawdza czy kod to prawidłowy IP drukarki (IP + IPv4)
    /// </summary>
    public static bool IsValidPrinterIP(string code)
    {
        if (string.IsNullOrWhiteSpace(code)) return false;
        return PrinterIpPattern.IsMatch(code);
    }

    /// <summary>
    /// Sprawdza czy kod to prawidłowy SSCC (18 cyfr)
    /// </summary>
    public static bool IsValidSSCC(string code)
    {
        if (string.IsNullOrWhiteSpace(code)) return false;
        return SsccPattern.IsMatch(code);
    }

    /// <summary>
    /// Sprawdza czy kod to prawidłowy DS (DS + 8 cyfr)
    /// </summary>
    public static bool IsValidDS(string code)
    {
        if (string.IsNullOrWhiteSpace(code)) return false;
        return DsPattern.IsMatch(code);
    }

    /// <summary>
    /// Sprawdza czy kod to potencjalny GS1 (zaczyna się od AI)
    /// </summary>
    public static bool IsValidGS1(string code)
    {
        if (string.IsNullOrWhiteSpace(code)) return false;
        return Gs1Pattern.IsMatch(code);
    }

    /// <summary>
    /// Wyodrębnia numer LK bez prefiksu
    /// </summary>
    public static string ExtractLKNumber(string lkCode)
    {
        if (!IsValidLK(lkCode)) return lkCode;
        return lkCode[2..];
    }

    /// <summary>
    /// Wyodrębnia adres IP z kodu drukarki (usuwa prefiks IP)
    /// </summary>
    public static string ExtractIPFromPrinterCode(string printerCode)
    {
        var match = PrinterIpPattern.Match(printerCode);
        return match.Success ? match.Groups[1].Value : printerCode;
    }

    /// <summary>
    /// Waliduje czy string jest prawidłowym adresem IPv4
    /// </summary>
    public static bool IsValidIPAddress(string ipString)
    {
        return IPAddress.TryParse(ipString, out var ipAddress) && 
               ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork;
    }

    /// <summary>
    /// Waliduje ilość (musi być > 0)
    /// </summary>
    public static bool IsValidQuantity(decimal? quantity)
    {
        return quantity.HasValue && quantity.Value > 0;
    }

    /// <summary>
    /// Waliduje ilość palet dla generowania DS (1-100)
    /// </summary>
    public static bool IsValidPalletCount(int count)
    {
        return count >= 1 && count <= 100;
    }

    /// <summary>
    /// Waliduje czy wymagana partia jest podana
    /// </summary>
    public static (bool IsValid, string? ErrorMessage) ValidateRequiredLot(string? lot, bool isRequired)
    {
        if (!isRequired) return (true, null);
        
        if (string.IsNullOrWhiteSpace(lot))
            return (false, "Partia jest wymagana dla tego towaru");
        
        return (true, null);
    }

    /// <summary>
    /// Waliduje czy wymagana data ważności jest podana
    /// </summary>
    public static (bool IsValid, string? ErrorMessage) ValidateRequiredExpiryDate(DateTime? expiryDate, bool isRequired)
    {
        if (!isRequired) return (true, null);
        
        if (!expiryDate.HasValue)
            return (false, "Data ważności jest wymagana dla tego towaru");
        
        if (expiryDate.Value.Date <= DateTime.Today)
            return (false, "Data ważności musi być w przyszłości");
        
        return (true, null);
    }
}
