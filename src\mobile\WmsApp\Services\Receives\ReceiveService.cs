using Microsoft.Extensions.Logging;
using Refit;
using WmsApp.Models.Receives;
using System.Linq;
using System.Net;

namespace WmsApp.Services.Receives;

/// <summary>
/// Główny serwis biznesowy dla modułu dostaw
/// Orkiestruje logikę biznesową i komunikację z API
/// </summary>
public interface IReceiveService
{
    // Selekcja dostaw
    Task<ReceiveDto[]> GetAvailableReceivesAsync();
    Task<bool> ClaimReceiveAsync(string lk, string deviceId);
    Task<bool> ReleaseReceiveAsync(string lk, string deviceId);
    
    // Generowanie DS
    Task<TypPaletyDto[]> GetTypyPaletAsync();
    Task<string[]> GetDrukarkiAsync();
    Task<GeneratedDsResponse> GenerateDsAsync(string lk, int typPaletyId, int iloscPalet, string? drukarkaNazwa, bool czyDrukowac);
    
    // Parsowanie skanów
    Task<ProcessGs1ScanResponse> ParseGs1ScanAsync(string gs1Code);
    Task<ParseReceiveScanResponse> ParseReceiveScanAsync(string scanData, int? listControlId = null, string? deviceId = null, string context = "delivery");
    Task<KodDto[]> SearchKodyAsync(string query, int limit = 10);
    Task<KodDto> GetKodByIdAsync(int kodId);
    Task<KodDto> GetKodByCodeAsync(string kod, int? systemId = null);
    
    // Rejestracja pozycji - stara metoda (zostanie zastąpiona)
    Task<RegisterPositionResponse> RegisterPositionAsync(RegisterPositionRequest request);
    Task<RegisterPositionResponse> ConfirmRegisterPositionAsync(RegisterPositionRequest request);
    
    // Nowe metody zgodne z backendem
    Task<CarrierDto> CreateCarrierAsync(int deliveryId, int typPaletyId, bool drukowac = false, string? drukarkaIp = null);
    Task<ReceiveItemDto> CreateReceiveItemAsync(int deliveryId, CreateReceiveItemRequest request);
    Task<CarrierDto[]> GetDeliveryCarriersAsync(int deliveryId);
    Task<bool> CompleteCarrierAsync(int deliveryId, int paletaId);
    
    // Zarządzanie nośnikami
    Task<GetNosnikPositionsResponse> GetNosnikPositionsAsync(string nosnikCode);
    Task CompleteNosnikAsync(string nosnikCode, string deviceId);
    
    // Awizacje
    Task<AwizacjaPositionDto[]> GetAwizacjaPositionsAsync(string lk);
    
    // Zakończenie sesji
    Task<FinishReceiveSessionResponse> FinishReceiveSessionAsync(string lk, string deviceId);
}

public class ReceiveService : IReceiveService
{
    private readonly IReceiveApiClient _apiClient;
    private readonly ILogger<ReceiveService> _logger;
    private readonly ReceiveCodeValidationService _validationService;

    public ReceiveService(
        IReceiveApiClient apiClient, 
        ILogger<ReceiveService> logger,
        ReceiveCodeValidationService validationService)
    {
        _apiClient = apiClient;
        _logger = logger;
        _validationService = validationService;
    }

    public async Task<ReceiveDto[]> GetAvailableReceivesAsync()
    {
        try
        {
            _logger.LogInformation("Pobieranie listy dostępnych dostaw");
            var response = await _apiClient.GetReceivesAsync(magazynId: null, limit: 100, includeAssigned: false);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                var list = response.Content.Receives
                    .Select(r => new ReceiveDto
                    {
                        Id = r.Id,
                        LK = string.IsNullOrWhiteSpace(r.Lk) ? $"LK{r.Id}" : r.Lk,
                        NumerZamowienia = r.DokumentDostawy,
                        SystemNazwa = r.SystemNazwa,
                        MiejsceDostawy = r.MiejsceDostawy,
                        Data = r.Data,
                        IsOccupied = r.IsAssigned,
                        OccupiedBy = r.RealizujacyPracownikId?.ToString()
                    })
                    .ToArray();
                
                _logger.LogInformation("Pobrano {Count} dostaw", list.Length);
                return list;
            }
            
            _logger.LogWarning("Nie udało się pobrać listy dostaw. StatusCode: {StatusCode}", response.StatusCode);
            return Array.Empty<ReceiveDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania listy dostaw");
            throw;
        }
    }

    public async Task<bool> ClaimReceiveAsync(string lk, string deviceId)
    {
        try
        {
            _logger.LogInformation("Próba claim dostawy {LK} przez device {DeviceId}", lk, deviceId);
            
            // Wyciągnij ID z LK (np. LK123 -> 123)
            var id = ParseLkToId(lk);
            if (id == null)
            {
                _logger.LogWarning("Nie można sparsować LK do ID: {LK}", lk);
                return false;
            }
            
            var response = await _apiClient.ClaimReceiveAsync(id.Value);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Pomyślnie zaclaimowano dostawę {LK}", lk);
                return true;
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
            {
                _logger.LogWarning("Dostawa {LK} jest już zajęta przez innego użytkownika", lk);
                return false;
            }
            
            _logger.LogWarning("Nie udało się zaclaimować dostawy {LK}. StatusCode: {StatusCode}", lk, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas claim dostawy {LK}", lk);
            throw;
        }
    }

    public async Task<bool> ReleaseReceiveAsync(string lk, string deviceId)
    {
        try
        {
            _logger.LogInformation("Zwalnianie claim dostawy {LK} przez device {DeviceId}", lk, deviceId);
            
            var id = ParseLkToId(lk);
            if (id == null)
            {
                _logger.LogWarning("Nie można sparsować LK do ID podczas release: {LK}", lk);
                return false;
            }
            
            var response = await _apiClient.ReleaseReceiveAsync(id.Value);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Pomyślnie zwolniono claim dostawy {LK}", lk);
                return true;
            }
            
            _logger.LogWarning("Nie udało się zwolnić claim dostawy {LK}. StatusCode: {StatusCode}", lk, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas release claim dostawy {LK}", lk);
            throw;
        }
    }

    public async Task<TypPaletyDto[]> GetTypyPaletAsync()
    {
        try
        {
            var response = await _apiClient.GetTypyPaletAsync();
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content;
            }
            
            _logger.LogWarning("Nie udało się pobrać typów palet. StatusCode: {StatusCode}", response.StatusCode);
            return Array.Empty<TypPaletyDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania typów palet");
            throw;
        }
    }

    public async Task<string[]> GetDrukarkiAsync()
    {
        try
        {
            var response = await _apiClient.GetPrintersAsync(onlyActive: true);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                // Zwracamy listę adresów IP drukarek
                return response.Content
                    .Where(p => p.IsActive)
                    .Select(p => string.IsNullOrWhiteSpace(p.IpAddress) ? p.Nazwa : p.IpAddress)
                    .ToArray();
            }
            
            _logger.LogWarning("Nie udało się pobrać listy drukarek. StatusCode: {StatusCode}", response.StatusCode);
            return Array.Empty<string>();
        }
        catch (ApiException apiEx) when ((int)apiEx.StatusCode == 404)
        {
            // Fallback: brak endpointu w backendzie – zwróć pustą listę
            _logger.LogWarning("Endpoint drukarek niedostępny (404) – zwracam pustą listę");
            return Array.Empty<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania listy drukarek");
            throw;
        }
    }

    public async Task<GeneratedDsResponse> GenerateDsAsync(string lk, int typPaletyId, int iloscPalet, string? drukarkaNazwa, bool czyDrukowac)
    {
        try
        {
            // Walidacja wejściowa
            if (!ReceiveCodeValidationService.IsValidPalletCount(iloscPalet))
            {
                throw new ArgumentException("Nieprawidłowa ilość palet (1-100)");
            }

            _logger.LogInformation("Generowanie {Count} DS dla dostawy {LK}", iloscPalet, lk);
            
            var request = new GenerateDsRequest
            {
                LK = lk,
                TypPaletyId = typPaletyId,
                IloscPalet = iloscPalet,
                DrukarkaNazwa = drukarkaNazwa,
                CzyDrukowac = czyDrukowac
            };
            
            var response = await _apiClient.GenerateDsAsync(request);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                _logger.LogInformation("Wygenerowano {Count} kodów DS dla dostawy {LK}", 
                    response.Content.GeneratedCodes.Length, lk);
                return response.Content;
            }
            
            _logger.LogWarning("Nie udało się wygenerować DS. StatusCode: {StatusCode}", response.StatusCode);
            return new GeneratedDsResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas generowania DS dla dostawy {LK}", lk);
            throw;
        }
    }

    public async Task<ProcessGs1ScanResponse> ParseGs1ScanAsync(string gs1Code)
    {
        try
        {
            _logger.LogInformation("Parsowanie kodu GS1: {Gs1Code}", gs1Code.Substring(0, Math.Min(20, gs1Code.Length)) + "...");
            
            var request = new ProcessGs1ScanRequest
            {
                Gs1Code = gs1Code
            };
            
            var response = await _apiClient.ParseGs1ScanAsync(request);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                _logger.LogInformation("Pomyślnie sparsowano kod GS1. SSCC: {SSCC}, EAN: {EAN}", 
                    response.Content.SSCC, response.Content.EAN);
                return response.Content;
            }
            
            _logger.LogWarning("Nie udało się sparsować kodu GS1. StatusCode: {StatusCode}", response.StatusCode);
            return new ProcessGs1ScanResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas parsowania kodu GS1");
            throw;
        }
    }

    public async Task<ParseReceiveScanResponse> ParseReceiveScanAsync(string scanData, int? listControlId = null, string? deviceId = null, string context = "delivery")
    {
        try
        {
            _logger.LogInformation("Parsowanie skanu: {ScanData}", scanData.Substring(0, Math.Min(20, scanData.Length)) + "...");
            
            var request = new ParseScanRequest
            {
                ScanData = scanData,
                ListControlId = listControlId,
                DeviceId = deviceId ?? "MAUI-App",
                Context = context
            };
            
            var response = await _apiClient.ParseReceiveScanAsync(request);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                _logger.LogInformation("Pomyślnie sparsowano skan. Sukces: {IsSuccess}, Typ: {ScanType}, IzPrefix: {HasIzPrefix}", 
                    response.Content.IsSuccess, response.Content.ScanType, response.Content.HasIzPrefix);
                return response.Content;
            }
            
            _logger.LogWarning("Nie udało się sparsować skanu. StatusCode: {StatusCode}", response.StatusCode);
            return new ParseReceiveScanResponse { IsSuccess = false, ErrorMessage = "Błąd parsowania skanu", RawScanData = scanData };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas parsowania skanu");
            return new ParseReceiveScanResponse { IsSuccess = false, ErrorMessage = ex.Message, RawScanData = scanData };
        }
    }

    public async Task<KodDto[]> SearchKodyAsync(string query, int limit = 10)
    {
        try
        {
            var response = await _apiClient.SearchKodyAsync(query, limit);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content;
            }
            
            return Array.Empty<KodDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyszukiwania kodów dla query: {Query}", query);
            throw;
        }
    }

    public async Task<KodDto> GetKodByIdAsync(int kodId)
    {
        try
        {
            var response = await _apiClient.GetKodByIdAsync(kodId);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content;
            }
            
            throw new InvalidOperationException($"Nie znaleziono kodu o ID {kodId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania kodu {KodId}", kodId);
            throw;
        }
    }

    public async Task<KodDto> GetKodByCodeAsync(string kod, int? systemId = null)
    {
        try
        {
            var response = await _apiClient.GetKodByCodeAsync(kod, systemId);
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content;
            }
            throw new InvalidOperationException($"Nie znaleziono kodu '{kod}'{(systemId.HasValue ? $" w systemie {systemId}" : string.Empty)}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania kodu po wartości {Kod} (systemId={SystemId})", kod, systemId);
            throw;
        }
    }

    public async Task<RegisterPositionResponse> RegisterPositionAsync(RegisterPositionRequest request)
    {
        try
        {
            _logger.LogInformation("Rejestracja pozycji dla LK {LK}, kod {KodId}, nośnik {Nosnik} (now using new backend endpoints)", 
                request.LK, request.KodId, request.NosnikCode ?? "(Auto)");

            // 1. Parsuj LK na delivery ID
            var deliveryId = ParseLkToId(request.LK);
            if (deliveryId == null)
            {
                _logger.LogError("Nie można sparsować LK {LK} na delivery ID", request.LK);
                throw new ArgumentException($"Nieprawidłowy format LK: {request.LK}");
            }

            // 1.5. Upewnij się, że dostawa jest zaclaimowana (dodatkowe zabezpieczenie)
            var deviceId = request.DeviceId;
            _logger.LogInformation("Sprawdzam claim dla dostawy {LK} przez device {DeviceId}", request.LK, deviceId);
            
            // Spróbuj ponownie zaclaimować na wszelki wypadek (idempotentne)
            var claimResult = await ClaimReceiveAsync(request.LK, deviceId);
            if (!claimResult)
            {
                _logger.LogWarning("Nie udało się ponownie zaclaimować dostawy {LK}. To może oznaczać problem ze stanem dostawy.", request.LK);
                // Kontynuujemy mimo to - może dostawa już jest zaclaimowana
            }
            else
            {
                _logger.LogInformation("Ponownie potwierdzono claim dostawy {LK}", request.LK);
            }

            CarrierDto carrier;
            bool wasCarrierCreated = false;

            // 2. Obsłuż nośnik: znajdź istniejący lub stwórz nowy
            if (!string.IsNullOrWhiteSpace(request.NosnikCode) && request.NosnikCode != "(Auto)")
            {
                // Użytkownik podał konkretny nośnik - znajdź go w istniejących
                var existingCarriers = await GetDeliveryCarriersAsync(deliveryId.Value);
                var existingCarrier = existingCarriers.FirstOrDefault(c => c.Kod == request.NosnikCode);
                
                if (existingCarrier != null)
                {
                    carrier = existingCarrier;
                    _logger.LogInformation("Użyto istniejącego nośnika {Kod}", carrier.Kod);
                }
                else
                {
                    // Nośnik nie istnieje - to błąd, ponieważ użytkownik podał konkretny kod
                    _logger.LogError("Nie znaleziono nośnika {NosnikCode} dla dostawy {DeliveryId}", request.NosnikCode, deliveryId);
                    throw new InvalidOperationException($"Nie znaleziono nośnika {request.NosnikCode} dla dostawy {request.LK}");
                }
            }
            else
            {
                // Tryb Auto - stwórz nowy nośnik
                if (!request.TypPaletyId.HasValue)
                {
                    _logger.LogError("Wymagany TypPaletyId dla trybu Auto");
                    throw new ArgumentException("Typ palety jest wymagany w trybie automatycznym");
                }

                carrier = await CreateCarrierAsync(deliveryId.Value, request.TypPaletyId.Value, drukowac: false);
                wasCarrierCreated = true;
                _logger.LogInformation("Utworzono nowy nośnik {Kod} w trybie Auto", carrier.Kod);
            }

            // 3. Stwórz pozycję na nośniku
            var createItemRequest = new CreateReceiveItemRequest
            {
                PaletaId = carrier.PaletaId,
                KodId = request.KodId,
                Lot = request.Partia,
                DataProd = request.DataProdukcji != default ? DateOnly.FromDateTime(request.DataProdukcji) : null,
                DataWaznosci = request.DataWaznosci.HasValue ? DateOnly.FromDateTime(request.DataWaznosci.Value) : null,
                Ilosc = request.IloscSztuk,
                Sscc = null, // SSCC zostanie nadany przez backend jeśli potrzeba
                Certyfikat = request.Certyfikat
            };

            var receiveItem = await CreateReceiveItemAsync(deliveryId.Value, createItemRequest);
            
            _logger.LogInformation("Pomyślnie zarejestrowano pozycję {ItemId} na nośniku {NosnikCode}", 
                receiveItem.Id, carrier.Kod);

            // 4. Zwróć odpowiedź w formacie kompatybilnym z UI
            return new RegisterPositionResponse
            {
                NosnikCode = carrier.Kod,
                WasCreated = wasCarrierCreated,
                ValidationWarning = null, // TODO: implementacja porównania z awizacją
                RequiresConfirmation = false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas rejestracji pozycji dla LK {LK}", request.LK);
            throw;
        }
    }

    public async Task<RegisterPositionResponse> ConfirmRegisterPositionAsync(RegisterPositionRequest request)
    {
        try
        {
            _logger.LogInformation("Potwierdzenie rejestracji pozycji dla LK {LK}", request.LK);
            
            var response = await _apiClient.ConfirmRegisterPositionAsync(request);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content;
            }
            
            throw new InvalidOperationException("Błąd potwierdzenia rejestracji pozycji");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas potwierdzenia rejestracji pozycji");
            throw;
        }
    }

    public async Task<GetNosnikPositionsResponse> GetNosnikPositionsAsync(string nosnikCode)
    {
        try
        {
            var response = await _apiClient.GetNosnikPositionsAsync(nosnikCode);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content;
            }
            
            return new GetNosnikPositionsResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania pozycji nośnika {NosnikCode}", nosnikCode);
            throw;
        }
    }

    public async Task CompleteNosnikAsync(string nosnikCode, string deviceId)
    {
        try
        {
            _logger.LogInformation("Oznaczanie nośnika {NosnikCode} jako kompletny", nosnikCode);
            
            var request = new CompleteNosnikRequest
            {
                NosnikCode = nosnikCode,
                DeviceId = deviceId
            };
            
            var response = await _apiClient.CompleteNosnikAsync(request);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Nie udało się oznaczyć nośnika jako kompletny. StatusCode: {StatusCode}", response.StatusCode);
                throw new InvalidOperationException("Błąd oznaczania nośnika jako kompletny");
            }
            
            _logger.LogInformation("Pomyślnie oznaczono nośnik {NosnikCode} jako kompletny", nosnikCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas oznaczania nośnika jako kompletny");
            throw;
        }
    }

    public async Task<AwizacjaPositionDto[]> GetAwizacjaPositionsAsync(string lk)
    {
        try
        {
            var response = await _apiClient.GetAwizacjaPositionsAsync(lk);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content;
            }
            
            return Array.Empty<AwizacjaPositionDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania pozycji awizacji dla {LK}", lk);
            throw;
        }
    }

    public async Task<FinishReceiveSessionResponse> FinishReceiveSessionAsync(string lk, string deviceId)
    {
        try
        {
            _logger.LogInformation("Zakończenie sesji dostaw dla LK {LK}", lk);
            
            var request = new FinishReceiveSessionRequest
            {
                LK = lk,
                DeviceId = deviceId
            };
            
            var response = await _apiClient.FinishReceiveSessionAsync(request);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                _logger.LogInformation("Pomyślnie zakończono sesję dla LK {LK}. CanClose: {CanClose}", 
                    lk, response.Content.CanClose);
                return response.Content;
            }
            
            _logger.LogWarning("Nie udało się zakończyć sesji. StatusCode: {StatusCode}", response.StatusCode);
            return new FinishReceiveSessionResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas kończenia sesji dla LK {LK}", lk);
            throw;
        }
    }

    // ===== NOWE METODY ZGODNE Z BACKENDEM =====

    public async Task<CarrierDto> CreateCarrierAsync(int deliveryId, int typPaletyId, bool drukowac = false, string? drukarkaIp = null)
    {
        try
        {
            _logger.LogInformation("Tworzenie nośnika dla dostawy {DeliveryId}, typ palety {TypPaletyId}", deliveryId, typPaletyId);
            
            var request = new CreateCarrierRequest
            {
                TypPaletyId = typPaletyId,
                Drukowac = drukowac,
                DrukarkaIp = drukarkaIp
            };
            
            var response = await _apiClient.CreateCarrierAsync(deliveryId, request);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                _logger.LogInformation("Pomyślnie utworzono nośnik {Kod} dla dostawy {DeliveryId}", 
                    response.Content.Kod, deliveryId);
                return response.Content;
            }
            
            _logger.LogWarning("Nie udało się utworzyć nośnika. StatusCode: {StatusCode}", response.StatusCode);
            throw new InvalidOperationException($"Błąd tworzenia nośnika dla dostawy {deliveryId}");
        }
        catch (ApiException apiEx) when (apiEx.StatusCode == System.Net.HttpStatusCode.Conflict)
        {
            _logger.LogError(apiEx, "Backend odrzucił twórz nośnika dla dostawy {DeliveryId} (409 Conflict). Prawdopodobnie dostawa nie jest przypisana do pracownika.", deliveryId);
            throw new InvalidOperationException($"Dostawa {deliveryId} nie jest przypisana do tego pracownika. Sprawdź stan dostawy i uprawnienia.", apiEx);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas tworzenia nośnika dla dostawy {DeliveryId}", deliveryId);
            throw;
        }
    }

    public async Task<ReceiveItemDto> CreateReceiveItemAsync(int deliveryId, CreateReceiveItemRequest request)
    {
        try
        {
            _logger.LogInformation("Dodawanie pozycji do dostawy {DeliveryId}, paleta {PaletaId}, kod {KodId}", 
                deliveryId, request.PaletaId, request.KodId);
            
            var response = await _apiClient.CreateReceiveItemAsync(deliveryId, request);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                _logger.LogInformation("Pomyślnie dodano pozycję {Id} do dostawy {DeliveryId}", 
                    response.Content.Id, deliveryId);
                return response.Content;
            }
            
            _logger.LogWarning("Nie udało się dodać pozycji. StatusCode: {StatusCode}", response.StatusCode);
            throw new InvalidOperationException($"Błąd dodawania pozycji do dostawy {deliveryId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas dodawania pozycji do dostawy {DeliveryId}", deliveryId);
            throw;
        }
    }

    public async Task<CarrierDto[]> GetDeliveryCarriersAsync(int deliveryId)
    {
        try
        {
            _logger.LogInformation("Pobieranie nośników dla dostawy {DeliveryId}", deliveryId);
            
            var response = await _apiClient.GetDeliveryCarriersAsync(deliveryId);
            
            if (response.IsSuccessStatusCode && response.Content != null)
            {
                _logger.LogInformation("Pobrano {Count} nośników dla dostawy {DeliveryId}", 
                    response.Content.Count, deliveryId);
                return response.Content.ToArray();
            }
            
            _logger.LogWarning("Nie udało się pobrać nośników. StatusCode: {StatusCode}", response.StatusCode);
            return Array.Empty<CarrierDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania nośników dla dostawy {DeliveryId}", deliveryId);
            throw;
        }
    }

    public async Task<bool> CompleteCarrierAsync(int deliveryId, int paletaId)
    {
        try
        {
            _logger.LogInformation("Oznaczanie nośnika {PaletaId} jako ukończony w dostawie {DeliveryId}", paletaId, deliveryId);
            
            var response = await _apiClient.CompleteCarrierAsync(deliveryId, paletaId);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Pomyślnie oznaczono nośnik {PaletaId} jako ukończony", paletaId);
                return true;
            }
            
            _logger.LogWarning("Nie udało się oznaczyć nośnika jako ukończony. StatusCode: {StatusCode}", response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas oznaczania nośnika {PaletaId} jako ukończony", paletaId);
            throw;
        }
    }

    private static int? ParseLkToId(string lk)
    {
        if (string.IsNullOrWhiteSpace(lk)) return null;
        var s = lk.Trim().ToUpperInvariant();
        if (s.StartsWith("LK")) s = s.Substring(2);
        if (int.TryParse(s, out var id)) return id;
        return null;
    }
}
