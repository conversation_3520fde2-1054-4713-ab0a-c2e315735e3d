using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using WmsApp.Models.Receives;
using WmsApp.Services.Receives;

namespace WmsApp.ViewModels.Receives;

/// <summary>
/// ViewModel podwidoku wyboru towaru (Awizacja | Kartoteka)
/// </summary>
public partial class ProductPickerViewModel : ObservableObject
{
    private readonly IReceiveService _receiveService;
    private readonly ILogger<ProductPickerViewModel> _logger;

    private TaskCompletionSource<ProductSelection?>? _tcs;
    private bool _isSelectionProcessing = false;

    [ObservableProperty]
    private string lk = string.Empty;

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private bool isAwizacjaTab = true;

    public bool IsKartotekaTab => !IsAwizacjaTab;

    [ObservableProperty]
    private string searchQuery = string.Empty;

    [ObservableProperty]
    private AwizacjaPositionDto? selectedAwizacjaItem;

    [ObservableProperty]
    private KodDto? selectedKodItem;

    public ObservableCollection<AwizacjaPositionDto> AwizacjaPositions { get; } = new();
    public ObservableCollection<KodDto> SearchResults { get; } = new();

    public ProductPickerViewModel(IReceiveService receiveService, ILogger<ProductPickerViewModel> logger)
    {
        _receiveService = receiveService;
        _logger = logger;
    }

    partial void OnSelectedAwizacjaItemChanged(AwizacjaPositionDto? value)
    {
        if (value != null && _tcs != null)
        {
            _logger.LogInformation("DEBUG: OnSelectedAwizacjaItemChanged - item selected: {Kod}", value.TowarKod);
            
            // Wykonaj selekcję asynchronicznie, aby uniknąć problemów z cyklem życia kontrolki
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await Task.Delay(100); // Krótkie opóźnienie dla stabilności UI
                await SelectAwizacjaAsync(value);
            });
        }
    }

    partial void OnSelectedKodItemChanged(KodDto? value)
    {
        if (value != null && _tcs != null)
        {
            _logger.LogInformation("DEBUG: OnSelectedKodItemChanged - kod selected: {Kod}", value.Kod);
            
            // Wykonaj selekcję asynchronicznie, aby uniknąć problemów z cyklem życia kontrolki
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await Task.Delay(100); // Krótkie opóźnienie dla stabilności UI
                SelectCode(value);
            });
        }
    }

    public void Init(string lk, TaskCompletionSource<ProductSelection?> tcs)
    {
        _logger.LogInformation("DEBUG: ProductPickerViewModel.Init wywołane z LK={LK}, TCS={TCS}", lk, tcs != null ? "not null" : "null");
        
        // Wyczyść poprzedni stan
        CleanupState();
        
        Lk = lk;
        _tcs = tcs;
        _ = LoadAwizacjaAsync();
    }
    
    private void CleanupState()
    {
        // Bezpieczne czyszczenie stanu - unikamy manipulacji SelectedItem
        AwizacjaPositions.Clear();
        SearchResults.Clear();
        SearchQuery = string.Empty;
        ErrorMessage = string.Empty;
        IsAwizacjaTab = true;
    }

    [RelayCommand]
    private async Task LoadAwizacjaAsync()
    {
        if (string.IsNullOrWhiteSpace(Lk)) return;
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var items = await _receiveService.GetAwizacjaPositionsAsync(Lk);
            MainThread.BeginInvokeOnMainThread(() =>
            {
                AwizacjaPositions.Clear();
                foreach (var it in items)
                    AwizacjaPositions.Add(it);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania pozycji awizacji dla {LK}", Lk);
            ErrorMessage = $"Błąd ładowania awizacji: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void SwitchToAwizacja()
    {
        IsAwizacjaTab = true;
        ErrorMessage = string.Empty;
        OnPropertyChanged(nameof(IsKartotekaTab));
    }

    [RelayCommand]
    private void SwitchToKartoteka()
    {
        IsAwizacjaTab = false;
        ErrorMessage = string.Empty;
        SearchResults.Clear();
        OnPropertyChanged(nameof(IsKartotekaTab));
    }

    [RelayCommand]
    private async Task SearchAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchQuery) || SearchQuery.Length < 2)
            return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var results = await _receiveService.SearchKodyAsync(SearchQuery, 15);
            MainThread.BeginInvokeOnMainThread(() =>
            {
                SearchResults.Clear();
                foreach (var r in results)
                    SearchResults.Add(r);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyszukiwania kodów: {Query}", SearchQuery);
            ErrorMessage = $"Błąd wyszukiwania: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task SelectAwizacjaAsync(AwizacjaPositionDto? item)
    {
        _logger.LogInformation("DEBUG: SelectAwizacjaAsync wywołane dla towaru {Kod}", item?.TowarKod ?? "null");
        
        if (item == null)
        {
            _logger.LogWarning("DEBUG: item jest null w SelectAwizacjaAsync");
            return;
        }
        
        if (_tcs == null)
        {
            _logger.LogWarning("DEBUG: TaskCompletionSource jest null w SelectAwizacjaAsync");
            return;
        }
        
        if (_isSelectionProcessing)
        {
            _logger.LogInformation("DEBUG: Już przetwarzanie selekcji, pomijam");
            return;
        }
        
        _isSelectionProcessing = true;
        
        try
        {
            _logger.LogInformation("DEBUG: Pobieranie KodDto dla {Kod}, SystemId={SystemId}", item.TowarKod, item.SystemId);
            
            // Dociągnij precyzyjny KodDto po (kod, systemId)
            var kod = await _receiveService.GetKodByCodeAsync(item.TowarKod, item.SystemId);
            
            _logger.LogInformation("DEBUG: Otrzymano KodDto: Id={Id}, Kod={Kod}", kod.Id, kod.Kod);
            
            var selection = new ProductSelection
            {
                KodId = kod.Id,
                KodValue = kod.Kod,
                KodNazwa = kod.Nazwa,
                PackagingUnit = (int)kod.IloscWOpakowaniu,
                CzyWymaganaPartia = kod.CzyWymaganaPartia,
                CzyWymaganaDataWaznosci = kod.CzyWymaganaDataWaznosci,
                Source = SelectionSource.Awizacja
            };
            
            _logger.LogInformation("DEBUG: Ustawianie rezultatu TCS");
            var setResult = _tcs.TrySetResult(selection);
            _logger.LogInformation("DEBUG: TCS.TrySetResult zwróciło {Result}", setResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyboru pozycji awizacji");
            ErrorMessage = $"Błąd wyboru: {ex.Message}";
            _tcs?.TrySetResult(null);
        }
        finally
        {
            _isSelectionProcessing = false;
        }
    }

    [RelayCommand]
    private void SelectCode(KodDto? kod)
    {
        _logger.LogInformation("DEBUG: SelectCode wywołane dla kodu {Kod}", kod?.Kod ?? "null");
        
        if (kod == null)
        {
            _logger.LogWarning("DEBUG: kod jest null w SelectCode");
            return;
        }
        
        if (_tcs == null)
        {
            _logger.LogWarning("DEBUG: TaskCompletionSource jest null w SelectCode");
            return;
        }
        
        if (_isSelectionProcessing)
        {
            _logger.LogInformation("DEBUG: Już przetwarzanie selekcji, pomijam");
            return;
        }
        
        _isSelectionProcessing = true;
        
        var selection = new ProductSelection
        {
            KodId = kod.Id,
            KodValue = kod.Kod,
            KodNazwa = kod.Nazwa,
            PackagingUnit = (int)kod.IloscWOpakowaniu,
            CzyWymaganaPartia = kod.CzyWymaganaPartia,
            CzyWymaganaDataWaznosci = kod.CzyWymaganaDataWaznosci,
            Source = SelectionSource.Kartoteka
        };
        
        try
        {
            _logger.LogInformation("DEBUG: Ustawianie rezultatu TCS dla kartoteki");
            var setResult = _tcs.TrySetResult(selection);
            _logger.LogInformation("DEBUG: TCS.TrySetResult zwróciło {Result}", setResult);
        }
        finally
        {
            _isSelectionProcessing = false;
        }
    }

    [RelayCommand]
    private void Close()
    {
        _logger.LogInformation("DEBUG: Close command wywołane");
        _tcs?.TrySetResult(null);
        CleanupState();
    }
}

