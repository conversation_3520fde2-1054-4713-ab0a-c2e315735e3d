<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.LoginPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:LoginViewModel"
             Title="{loc:Translate Key=Login_Title}"
             BackgroundColor="{DynamicResource Primary}">

    <ScrollView>
        <StackLayout Padding="40" Spacing="30" VerticalOptions="Center">
            
            <!-- App Logo/Title -->
            <Label Text="{loc:Translate Key=Login_AppName}" 
                   FontSize="32" 
                   FontAttributes="Bold" 
                   TextColor="White" 
                   HorizontalOptions="Center" />
            
            <Label Text="{loc:Translate Key=Login_Subtitle}" 
                   FontSize="16" 
                   TextColor="White" 
                   HorizontalOptions="Center" 
                   Opacity="0.8" />

            <!-- Login Form -->
            <Border BackgroundColor="White"
                    Padding="30">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="10" />
                </Border.StrokeShape>
                <Border.Shadow>
                    <Shadow />
                </Border.Shadow>
                
                <StackLayout Spacing="20">
                    
                    <!-- Language + Environment Selector -->
                    <StackLayout IsVisible="{Binding ShowEnvironmentSelector}" Spacing="10">
                        <Label Text="{loc:Translate Key=Login_Language_Label}"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />

                        <Picker ItemsSource="{Binding AvailableLanguages}"
                                SelectedItem="{Binding SelectedLanguage}"
                                Title="{loc:Translate Key=Common_Select}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}"
                                TitleColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}">
                            <Picker.ItemDisplayBinding>
                                <Binding Path="DisplayName" />
                            </Picker.ItemDisplayBinding>
                        </Picker>

                        <Label Text="{loc:Translate Key=Login_Environment_Label}" 
                               FontSize="14" 
                               FontAttributes="Bold" 
                               HorizontalOptions="Center" 
                               TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
                        
                        <Picker ItemsSource="{Binding AvailableEnvironments}"
                                SelectedItem="{Binding SelectedEnvironment}"
                                Title="{loc:Translate Key=Login_Environment_Picker_Title}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}"
                                TitleColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}">
                            <Picker.ItemDisplayBinding>
<Binding Path="." Converter="{StaticResource ApiEnvironmentDisplayConverter}" />
                            </Picker.ItemDisplayBinding>
                        </Picker>
                        
                        <!-- Environment Info Display -->
                        <Label Text="{Binding CurrentEnvironmentInfo}"
                               FontSize="10"
                               FontFamily="Monospace"
                               TextColor="{StaticResource Gray500}"
                               BackgroundColor="{StaticResource Gray100}"
                               Padding="8,4"
                               Margin="0,5"
                               LineBreakMode="WordWrap" />
                        
                        <BoxView HeightRequest="1" 
                                 BackgroundColor="{StaticResource Gray200}" 
                                 Margin="0,10" />
                    </StackLayout>
                    
                    <Label Text="Scan or enter your card number" 
                           FontSize="16" 
                           FontAttributes="Bold" 
                           TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}"
                           HorizontalOptions="Center" />

                    <!-- Card Number Entry -->
                    <Entry x:Name="CardNumberEntry"
                           Text="{Binding CardNumber}"
                           Placeholder="{loc:Translate Key=Login_CardNumber_Placeholder}"
                           Keyboard="Numeric"
                           TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}"
                           PlaceholderColor="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}"
                           IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}"
                           ReturnType="Go"
                           ReturnCommand="{Binding LoginCommand}" />

                    <!-- Scan Button -->
                    <Button Text="{loc:Translate Key=Login_ScanCard_Button}" 
                            Command="{Binding ScanCardCommand}"
                            BackgroundColor="{DynamicResource Secondary}"
                            TextColor="White"
                            CornerRadius="25"
                            HeightRequest="50"
                            FontSize="16"
                            IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />

                    <!-- Login Button -->
                    <Button Text="{loc:Translate Key=Login_Login_Button}" 
                            Command="{Binding LoginCommand}"
                            BackgroundColor="{DynamicResource Primary}"
                            TextColor="White"
                            CornerRadius="25"
                            HeightRequest="50"
                            FontSize="18"
                            FontAttributes="Bold"
                            IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />

                    <!-- Loading Indicator -->
                    <ActivityIndicator IsVisible="{Binding IsLoading}" 
                                       IsRunning="{Binding IsLoading}"
                                       Color="{DynamicResource Primary}" />

                    <!-- Error Message -->
                    <Label Text="{Binding ErrorMessage}"
                           TextColor="Red"
                           FontSize="14"
                           HorizontalOptions="Center"
                           IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}" />

                    <!-- Demo Mode Info -->
                    <Border BackgroundColor="{DynamicResource Gray100}"
                            Padding="12"
                            Margin="0,15,0,0">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="8" />
                        </Border.StrokeShape>
                        <StackLayout>
                            <Label Text="{loc:Translate Key=Login_DemoMode_Title}" 
                                   FontSize="14" 
                                   FontAttributes="Bold"
                                   TextColor="{DynamicResource Primary}"
                                   HorizontalOptions="Center" />
                            <Label Text="{loc:Translate Key=Login_DemoMode_Subtitle}" 
                                   FontSize="12" 
                                   TextColor="{DynamicResource Gray600}"
                                   HorizontalOptions="Center" />
                            <Label Text="{loc:Translate Key=Login_DemoMode_Items}" 
                                   FontSize="12" 
                                   FontAttributes="Bold"
                                   TextColor="{DynamicResource Secondary}"
                                   HorizontalOptions="Center"
                                   Margin="0,3,0,0" />
                        </StackLayout>
                    </Border>
                
                </StackLayout>
            </Border>

            <!-- App Info -->
            <Label Text="{loc:Translate Key=Login_Version_Label}" 
                   FontSize="12" 
                   TextColor="White" 
                   HorizontalOptions="Center" 
                   Opacity="0.6" />

        </StackLayout>
    </ScrollView>

</ContentPage>
