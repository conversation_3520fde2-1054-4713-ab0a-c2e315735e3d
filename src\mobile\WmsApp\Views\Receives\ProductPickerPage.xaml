<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.Receives.ProductPickerPage"
             x:Name="ProductPickerPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels.Receives;assembly=WmsApp"
             xmlns:models="clr-namespace:WmsApp.Models.Receives"
             x:DataType="viewmodels:ProductPickerViewModel"
             Title="Wybór towaru">

    <Grid Padding="10" RowDefinitions="Auto,*,Auto">
        <!-- Tabs -->
        <Grid Grid.Row="0" ColumnDefinitions="*,*" ColumnSpacing="8">
            <Button Text="Awizacja" Command="{Binding SwitchToAwizacjaCommand}"
                    BackgroundColor="{Binding IsAwizacjaTab, Converter={StaticResource ReceiveBoolToColorConverter}}"
                    TextColor="White" Grid.Column="0" />
            <Button Text="Kartoteka kodów" Command="{Binding SwitchToKartotekaCommand}"
                    BackgroundColor="{Binding IsKartotekaTab, Converter={StaticResource ReceiveBoolToColorConverter}}"
                    TextColor="White" Grid.Column="1" />
        </Grid>

        <!-- Content -->
        <Grid Grid.Row="1">
            <!-- Awizacja Tab -->
            <CollectionView ItemsSource="{Binding AwizacjaPositions}"
                            IsVisible="{Binding IsAwizacjaTab}"
                            BackgroundColor="LightGray"
                            SelectionMode="Single"
                            SelectedItem="{Binding SelectedAwizacjaItem}">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:AwizacjaPositionDto">
                        <Grid Padding="8" ColumnDefinitions="*,Auto" BackgroundColor="White" Margin="0,1">
                            <StackLayout Grid.Column="0" Spacing="1">
                                <Label Text="{Binding TowarKod}" FontAttributes="Bold" FontSize="13" />
                                <Label Text="{Binding TowarNazwa}" FontSize="12" TextColor="Gray" />
                            </StackLayout>

                            <Label Grid.Column="1" Text="{Binding SSCC}" FontSize="10" TextColor="Gray" VerticalOptions="Center" />
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>

            <!-- Kartoteka Tab -->
            <StackLayout IsVisible="{Binding IsAwizacjaTab, Converter={StaticResource InverseBoolConverter}}" Spacing="8">
                <SearchBar Placeholder="Szukaj kodu lub nazwy" Text="{Binding SearchQuery}" SearchCommand="{Binding SearchCommand}" />
                <CollectionView ItemsSource="{Binding SearchResults}" 
                                BackgroundColor="LightGray"
                                SelectionMode="Single"
                                SelectedItem="{Binding SelectedKodItem}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="models:KodDto">
                            <Grid Padding="10" ColumnDefinitions="*,Auto" BackgroundColor="White" Margin="0,1">
                                <StackLayout Grid.Column="0" Spacing="2">
                                    <Label Text="{Binding Kod}" FontAttributes="Bold" FontSize="14" />
                                    <Label Text="{Binding Nazwa}" FontSize="12" TextColor="Gray" />
                                </StackLayout>

                                <Label Grid.Column="1" Text="{Binding IloscWOpakowaniu, StringFormat='{0}/op'}" FontSize="11" VerticalOptions="Center" />
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </StackLayout>
        </Grid>

        <!-- Footer -->
        <Grid Grid.Row="2">
            <Button Text="Zamknij" Command="{Binding CloseCommand}" BackgroundColor="Gray" TextColor="White" />
        </Grid>

        <!-- Loading overlay -->
        <ActivityIndicator IsVisible="{Binding IsLoading}" IsRunning="{Binding IsLoading}" Color="{StaticResource Primary}" />

        <!-- Error message -->
        <Label Text="{Binding ErrorMessage}" TextColor="Red" BackgroundColor="LightPink" IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}" />
    </Grid>
</ContentPage>

