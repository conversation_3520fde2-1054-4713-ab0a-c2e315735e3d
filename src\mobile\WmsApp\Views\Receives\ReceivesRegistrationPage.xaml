<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.Receives.ReceivesRegistrationPage"
             x:Name="ReceivesRegistrationPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels.Receives;assembly=WmsApp"
             xmlns:models="clr-namespace:WmsApp.Models.Receives"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:ReceivesRegistrationViewModel"
             Title="{loc:Translate Key=ReceivesRegistration_Title}"
             Shell.NavBarIsVisible="True">

    <Grid>
        <!-- Main Content -->
        <ScrollView>
        <StackLayout Padding="15" Spacing="15">
            
            <!-- Header with current session info -->
            <Frame BackgroundColor="{StaticResource Primary}" HasShadow="True" CornerRadius="8">
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto">
                    <Label Text="{Binding CurrentLK, StringFormat='Dostawa: {0}'}" 
                           TextColor="White" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           Grid.Row="0" Grid.Column="0" />
                    <Label Text="{Binding CurrentNosnik, StringFormat='Nośnik: {0}'}" 
                           TextColor="White" 
                           FontSize="14"
                           Grid.Row="0" Grid.Column="1" />
<Label Text="{loc:Translate Key=ReceivesRegistration_HeaderTitle}" 
                           TextColor="White" 
                           FontSize="16" 
                           FontAttributes="Bold"
                           HorizontalOptions="Center"
                           Grid.Row="1" Grid.ColumnSpan="2" />
                </Grid>
            </Frame>

            <!-- Scan Input Section -->
            <Frame BackgroundColor="White" HasShadow="True" CornerRadius="8">
                <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=ReceivesRegistration_Scan_Label}" FontSize="16" FontAttributes="Bold" />
                    
                    <Entry x:Name="ScanEntry"
                           Text="{Binding ScanInput}"
Placeholder="{loc:Translate Key=ReceivesRegistration_Scan_Placeholder}"
                           FontSize="16"
                           HeightRequest="50"
                           BackgroundColor="LightGray"
                           ReturnCommand="{Binding ProcessScanCommand}"
                           ReturnType="Done" />
                    
<Button Text="{loc:Translate Key=ReceivesRegistration_Scan_Button}"
                            Command="{Binding ProcessScanCommand}"
                            BackgroundColor="{StaticResource Primary}"
                            TextColor="White"
                            FontSize="16"
                            HeightRequest="50" />
                </StackLayout>
            </Frame>

            <!-- Item Details Form -->
            <Frame BackgroundColor="White" HasShadow="True" CornerRadius="8">
                <StackLayout Spacing="12">
<Label Text="{loc:Translate Key=ReceivesRegistration_ItemData_Label}" FontSize="16" FontAttributes="Bold" />
                    
                    <!-- Towar Source Toggle -->
                    <Grid ColumnDefinitions="*,*">
<Button Text="{loc:Translate Key=ReceivesRegistration_Source_Awizacja_Button}" 
                                BackgroundColor="{Binding IsTowarSourceAwizacja, Converter={StaticResource ReceiveBoolToColorConverter}}"
                                TextColor="White"
                                Command="{Binding ToggleTowarSourceCommand}"
                                IsVisible="{Binding IsTowarSourceAwizacja, Converter={StaticResource InverseBoolConverter}}"
                                Grid.Column="0" />
<Button Text="{loc:Translate Key=ReceivesRegistration_Source_Kartoteka_Button}"
                                BackgroundColor="{Binding IsTowarSourceAwizacja, Converter={StaticResource ReceiveBoolToColorConverter}}"
                                TextColor="White" 
                                Command="{Binding ToggleTowarSourceCommand}"
                                IsVisible="{Binding IsTowarSourceAwizacja}"
                                Grid.Column="1" />
                    </Grid>

                    <!-- Towar Selection -->
                    <StackLayout Spacing="5">
<Label Text="{loc:Translate Key=ReceivesRegistration_Towar_Label}" FontSize="14" />
                        <Grid ColumnDefinitions="*,Auto">
                            <Entry Text="{Binding TowarKod}" 
Placeholder="{loc:Translate Key=ReceivesRegistration_Towar_Placeholder}"
                                   IsReadOnly="True"
                                   FontSize="14"
                                   BackgroundColor="LightGray"
                                   Grid.Column="0" />
<Button Text="{loc:Translate Key=ReceivesRegistration_Search_Button}"
                                    Command="{Binding OpenProductPickerCommand}"
                                    BackgroundColor="{StaticResource Secondary}"
                                    TextColor="White"
                                    FontSize="12"
                                    Padding="15,5"
                                    Grid.Column="1" />
                        </Grid>
<Entry x:Name="TowarSearchEntry"
                               Text="{Binding TowarQuery}"
                               Placeholder="{loc:Translate Key=ReceivesRegistration_Search_Placeholder}"
                               FontSize="12"
                               IsVisible="{Binding IsTowarSourceAwizacja, Converter={StaticResource InverseBoolConverter}}" />
                        <Label Text="{Binding TowarNazwa}" 
                               FontSize="12" 
                               TextColor="Gray" 
                               IsVisible="{Binding TowarNazwa, Converter={StaticResource StringToBoolConverter}}" />
                    </StackLayout>

                    <!-- Quantities -->
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <StackLayout Grid.Column="0">
<Label Text="{loc:Translate Key=ReceivesRegistration_Quantities_Opakowania_Label}" FontSize="14" />
                            <Entry Text="{Binding IloscOpakowan}" 
                                   Keyboard="Numeric"
                                   FontSize="14"
                                   BackgroundColor="LightGray"
                                   ReturnCommand="{Binding QuickConfirmCommand}"
                                   ReturnType="Done" />
                        </StackLayout>
                        <StackLayout Grid.Column="1">
<Label Text="{loc:Translate Key=ReceivesRegistration_Quantities_Sztuk_Label}" FontSize="14" />
                            <Entry Text="{Binding IloscSztuk}" 
                                   Keyboard="Numeric"
                                   FontSize="14"
                                   BackgroundColor="LightGray" />
                        </StackLayout>
                    </Grid>
                    
                    <Label Text="{Binding IloscWOpakowaniu, StringFormat='Sztuk w opakowaniu: {0}'}" 
                           FontSize="12" 
                           TextColor="Gray"
                           IsVisible="{Binding IloscWOpakowaniu, Converter={StaticResource DecimalToBoolConverter}}" />

                    <!-- Partia and Expiry -->
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <StackLayout Grid.Column="0">
<Label Text="{loc:Translate Key=ReceivesRegistration_Partia_Label}" FontSize="14" />
                            <Entry Text="{Binding Partia}" 
Placeholder="{loc:Translate Key=Common_Optional}"
                                   FontSize="14"
                                   BackgroundColor="LightGray" />
                        </StackLayout>
                        <StackLayout Grid.Column="1">
<Label Text="{loc:Translate Key=ReceivesRegistration_DataWaznosci_Label}" FontSize="14" />
                            <DatePicker Date="{Binding DataWaznosci, Converter={StaticResource NullableDateTimeConverter}}" 
                                        FontSize="14"
                                        BackgroundColor="LightGray" />
                        </StackLayout>
                    </Grid>

                    <!-- Production Date and Certificate -->
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <StackLayout Grid.Column="0">
<Label Text="{loc:Translate Key=ReceivesRegistration_DataProdukcji_Label}" FontSize="14" />
                            <DatePicker Date="{Binding DataProdukcji}" 
                                        FontSize="14"
                                        BackgroundColor="LightGray" />
                        </StackLayout>
                        <StackLayout Grid.Column="1">
<Label Text="{loc:Translate Key=ReceivesRegistration_Certyfikat_Label}" FontSize="14" />
                            <Entry Text="{Binding Certyfikat}" 
                                   Placeholder="Opcjonalnie"
                                   FontSize="14"
                                   BackgroundColor="LightGray" />
                        </StackLayout>
                    </Grid>

                    <!-- Pallet Type for Auto mode -->
                    <StackLayout IsVisible="{Binding CurrentNosnik, Converter={StaticResource StringEqualsConverter}, ConverterParameter=(Auto)}">
<Label Text="{loc:Translate Key=ReceivesRegistration_TypPaletyForAuto_Label}" FontSize="14" />
                        <Picker ItemsSource="{Binding TypyPalet}">
                            <Picker.ItemDisplayBinding>
                                <Binding Path="Nazwa" x:DataType="models:TypPaletyDto" />
                            </Picker.ItemDisplayBinding>
                            <Picker.SelectedItem>
                                <Binding Path="SelectedTypPaletyId" Converter="{StaticResource IdToTypPaletyConverter}" />
                            </Picker.SelectedItem>
                        </Picker>
                    </StackLayout>

                    <!-- Register Button -->
<Button Text="{loc:Translate Key=ReceivesRegistration_Register_Button}"
                            Command="{Binding RegisterPositionCommand}"
                            BackgroundColor="{StaticResource Primary}"
                            TextColor="White"
                            FontSize="16"
                            HeightRequest="50" />
                </StackLayout>
            </Frame>

            <!-- Actions -->
            <Grid ColumnDefinitions="*,*,*" ColumnSpacing="8">
<Button Text="{loc:Translate Key=Common_Preview}" 
                        Command="{Binding ShowPreviewCommand}"
                        BackgroundColor="{StaticResource Secondary}"
                        TextColor="White"
                        FontSize="14"
                        HeightRequest="45"
                        Grid.Column="0" />
                
<Button Text="{loc:Translate Key=ReceivesRegistration_Actions_CompleteNosnik_Button}"
                        Command="{Binding CompleteNosnikCommand}"
                        BackgroundColor="{StaticResource Tertiary}"
                        TextColor="White"
                        FontSize="12"
                        HeightRequest="45"
                        Grid.Column="1" />
                
<Button Text="{loc:Translate Key=ReceivesRegistration_Actions_Finish_Button}"
                        Command="{Binding FinishSessionCommand}"
                        BackgroundColor="DarkRed"
                        TextColor="White"
                        FontSize="14"
                        HeightRequest="45"
                        Grid.Column="2" />
            </Grid>

            <!-- Messages -->
            <Label Text="{Binding ErrorMessage}" 
                   TextColor="Red" 
                   FontSize="14" 
                   IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
                   BackgroundColor="LightPink"
                   Padding="10" />
                   
            <Label Text="{Binding SuccessMessage}" 
                   TextColor="Green" 
                   FontSize="14" 
                   IsVisible="{Binding SuccessMessage, Converter={StaticResource StringToBoolConverter}}"
                   BackgroundColor="LightGreen"
                   Padding="10" />

            <!-- Loading -->
            <ActivityIndicator IsVisible="{Binding IsLoading}" 
                               IsRunning="{Binding IsLoading}" 
                               Color="{StaticResource Primary}"
                               HeightRequest="50" />

            <!-- Awizacja Positions (when Awizacja mode) -->
            <Frame IsVisible="{Binding IsTowarSourceAwizacja}" 
                   BackgroundColor="White" 
                   HasShadow="True" 
                   CornerRadius="8">
                <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=ReceivesRegistration_AwizacjaPositions_Header}" FontSize="14" FontAttributes="Bold" />
                    <CollectionView ItemsSource="{Binding AwizacjaPositions}"
                                    HeightRequest="120"
                                    BackgroundColor="LightGray">
                        <CollectionView.ItemTemplate>
<DataTemplate x:DataType="models:AwizacjaPositionDto">
                                <Grid Padding="8" ColumnDefinitions="*,Auto" BackgroundColor="White" Margin="0,1">
                                    <Grid.GestureRecognizers>
<TapGestureRecognizer Command="{Binding Source={x:Reference ReceivesRegistrationPageRoot}, Path=BindingContext.SelectAwizacjaPositionCommand}"
                                                              CommandParameter="{Binding .}" />
                                    </Grid.GestureRecognizers>
                                    
                                    <StackLayout Grid.Column="0" Spacing="1">
                                        <Label Text="{Binding TowarKod}" FontAttributes="Bold" FontSize="12" />
                                        <Label Text="{Binding TowarNazwa}" FontSize="11" TextColor="Gray" />
                                        <Label Text="{Binding IloscPozostala, StringFormat='Pozostało: {0}'}" 
                                               FontSize="10" TextColor="DarkBlue" />
                                    </StackLayout>
                                    
                                    <Label Grid.Column="1" 
                                           Text="{Binding SSCC}" 
                                           FontSize="9" 
                                           TextColor="Gray"
                                           VerticalOptions="Center" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </StackLayout>
            </Frame>

        </StackLayout>
        </ScrollView>

        <!-- Confirmation Modal -->
        <Grid IsVisible="{Binding IsConfirmationModalVisible}"
          BackgroundColor="#80000000" 
          VerticalOptions="Fill" 
          HorizontalOptions="Fill">
        <Frame BackgroundColor="White" 
               HasShadow="True" 
               CornerRadius="10" 
               Margin="30"
               VerticalOptions="Center"
               HorizontalOptions="Fill">
            <StackLayout Spacing="20">
<Label Text="{loc:Translate Key=ReceivesRegistration_Confirmation_Title}" 
                       FontSize="18" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" />
                       
                <Label Text="{Binding ConfirmationMessage}" 
                       FontSize="14" 
                       HorizontalOptions="Center"
                       HorizontalTextAlignment="Center" />
                
                <Grid ColumnDefinitions="*,*" ColumnSpacing="15">
<Button Text="{loc:Translate Key=Common_Cancel}" 
                            Command="{Binding CancelConfirmationCommand}"
                            BackgroundColor="Gray"
                            TextColor="White"
                            Grid.Column="0" />
<Button Text="{loc:Translate Key=Common_Continue}" 
                            Command="{Binding ConfirmRegistrationCommand}"
                            BackgroundColor="{StaticResource Primary}"
                            TextColor="White"
                            Grid.Column="1" />
                </Grid>
            </StackLayout>
        </Frame>
        </Grid>

        <!-- Preview Modal -->
        <Grid IsVisible="{Binding IsPreviewModalVisible}"
          BackgroundColor="#80000000" 
          VerticalOptions="Fill" 
          HorizontalOptions="Fill">
        <Frame BackgroundColor="White" 
               HasShadow="True" 
               CornerRadius="10" 
               Margin="20"
               VerticalOptions="Center"
               HorizontalOptions="Fill">
            <StackLayout Spacing="15">
                <Label Text="{Binding CurrentNosnik, StringFormat='Podgląd nośnika: {0}'}" 
                       FontSize="16" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" />

                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto">
                    <Label Text="{Binding PreviewTotalSztuk, StringFormat='Razem sztuk: {0}'}" 
                           FontSize="14" FontAttributes="Bold"
                           Grid.Row="0" Grid.Column="0" />
                    <Label Text="{Binding PreviewTotalOpakowan, StringFormat='Razem opakowań: {0:F2}'}" 
                           FontSize="14" FontAttributes="Bold"
                           Grid.Row="0" Grid.Column="1" />
                </Grid>

                <CollectionView ItemsSource="{Binding PreviewPositions}"
                                HeightRequest="200"
                                BackgroundColor="LightGray">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="models:NosnikPositionDto">
                            <Grid Padding="8" ColumnDefinitions="*,Auto,Auto" BackgroundColor="White" Margin="0,1">
                                <StackLayout Grid.Column="0" Spacing="1">
                                    <Label Text="{Binding TowarKod}" FontAttributes="Bold" FontSize="12" />
                                    <Label Text="{Binding TowarNazwa}" FontSize="10" TextColor="Gray" />
                                    <Label Text="{Binding Partia, StringFormat='Partia: {0}'}" 
                                           FontSize="9" TextColor="DarkBlue"
                                           IsVisible="{Binding Partia, Converter={StaticResource StringToBoolConverter}}" />
                                </StackLayout>
                                
                                <Label Grid.Column="1" 
                                       Text="{Binding IloscSztuk, StringFormat='{0} szt'}" 
                                       FontSize="11" FontAttributes="Bold"
                                       VerticalOptions="Center" />
                                       
                                <Label Grid.Column="2" 
                                       Text="{Binding DataPrzyjecia, StringFormat='{0:HH:mm}'}" 
                                       FontSize="9" TextColor="Gray"
                                       VerticalOptions="Center" />
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!-- Preview Warnings -->
                <CollectionView ItemsSource="{Binding PreviewWarnings}"
                                IsVisible="{Binding PreviewWarnings, Converter={StaticResource CollectionToBoolConverter}}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="x:String">
                            <Label Text="{Binding}" 
                                   TextColor="Orange" 
                                   FontSize="12"
                                   BackgroundColor="LightYellow"
                                   Padding="5" />
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

<Button Text="{loc:Translate Key=ReceivesRegistration_Preview_Close_Button}" 
                        Command="{Binding ClosePreviewModalCommand}"
                        BackgroundColor="{StaticResource Primary}"
                        TextColor="White" />
            </StackLayout>
        </Frame>
        </Grid>

        <!-- Kod Selection Modal -->
        <Grid IsVisible="{Binding IsKodSelectionModalVisible}"
          BackgroundColor="#80000000" 
          VerticalOptions="Fill" 
          HorizontalOptions="Fill">
        <Frame BackgroundColor="White" 
               HasShadow="True" 
               CornerRadius="10" 
               Margin="25"
               VerticalOptions="Center"
               HorizontalOptions="Fill">
            <StackLayout Spacing="15">
<Label Text="{loc:Translate Key=ReceivesRegistration_KodSelection_Title}" 
                       FontSize="16" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" />

                <CollectionView ItemsSource="{Binding KodCandidates}"
                                HeightRequest="200"
                                BackgroundColor="LightGray">
                    <CollectionView.ItemTemplate>
<DataTemplate x:DataType="models:KodDto">
                            <Grid Padding="10" ColumnDefinitions="*,Auto" BackgroundColor="White" Margin="0,1">
                                <Grid.GestureRecognizers>
<TapGestureRecognizer Command="{Binding Source={x:Reference ReceivesRegistrationPageRoot}, Path=BindingContext.SelectKodCommand}"
                                                          CommandParameter="{Binding .}" />
                                </Grid.GestureRecognizers>
                                
                                <StackLayout Grid.Column="0" Spacing="2">
                                    <Label Text="{Binding Kod}" FontAttributes="Bold" FontSize="14" />
                                    <Label Text="{Binding Nazwa}" FontSize="12" TextColor="Gray" />
                                </StackLayout>
                                
                                <Label Grid.Column="1" 
                                       Text="{Binding IloscWOpakowaniu, StringFormat='{0}/op'}" 
                                       FontSize="11" TextColor="DarkBlue"
                                       VerticalOptions="Center" />
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

<Button Text="{loc:Translate Key=Common_Cancel}" 
                        Command="{Binding CloseKodSelectionModalCommand}"
                        BackgroundColor="Gray"
                        TextColor="White" />
            </StackLayout>
        </Frame>
        </Grid>
    </Grid>

</ContentPage>
