using WmsApp.ViewModels.Receives;

namespace WmsApp.Views.Receives;

public partial class ReceivesRegistrationPage : ContentPage
{
    public ReceivesRegistrationPage(ReceivesRegistrationViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public ReceivesRegistrationViewModel VM => (ReceivesRegistrationViewModel)BindingContext;

    protected override void OnAppearing()
    {
        base.OnAppearing();
        
        // Focus scan entry for immediate scanning
        ScanEntry.Focus();
    }
}
