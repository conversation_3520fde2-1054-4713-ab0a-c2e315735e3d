using WmsApp.ViewModels.Receives;

namespace WmsApp.Views.Receives;

public partial class ReceivesSelectionPage : ContentPage
{
    public ReceivesSelectionPage(ReceivesSelectionViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public ReceivesSelectionViewModel VM => (ReceivesSelectionViewModel)BindingContext;

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        // Auto-load data when page appears
        if (BindingContext is ReceivesSelectionViewModel viewModel)
        {
            await viewModel.LoadDataCommand.ExecuteAsync(null);
        }
        
        // Focus LK entry
        LkEntry.Focus();
    }
}
