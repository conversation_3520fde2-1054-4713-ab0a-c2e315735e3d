using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Testcontainers.MySql;
using Wms.Infrastructure.Data;

namespace Wms.IntegrationTests.Infrastructure;

public class WmsWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly MySqlContainer _mySqlContainer = new MySqlBuilder()
        .WithImage("mysql:8.0")
        .WithDatabase("wmstest")
        .WithUsername("testuser")
        .WithPassword("testpass")
        // Wyłącz głośne logi Testcontainers (np. "testcontainers.org ... Execute \"mysql ... SELECT 1;\"")
        .WithLogger(NullLogger.Instance)
        .WithCleanUp(true)
        .Build();

    public string ConnectionString { get; private set; } = null!;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<WmsDbContext>));

            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add test database context
            services.AddDbContext<WmsDbContext>(options =>
            {
                options.UseMySql(ConnectionString, ServerVersion.AutoDetect(ConnectionString));
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Configure test logging
            services.AddLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Warning);
            });

            // Build service provider and ensure database is created
            var serviceProvider = services.BuildServiceProvider();
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<WmsDbContext>();
            context.Database.EnsureCreated();
        });

        builder.UseEnvironment("Testing");
    }

    public async Task InitializeAsync()
    {
        await _mySqlContainer.StartAsync();
        ConnectionString = _mySqlContainer.GetConnectionString();
    }

    public new async Task DisposeAsync()
    {
        await _mySqlContainer.DisposeAsync();
        await base.DisposeAsync();
    }
}
