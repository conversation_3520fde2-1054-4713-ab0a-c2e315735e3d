using FluentAssertions;
using Wms.Domain.Exceptions;
using Wms.Domain.ValueObjects;
using Xunit;

namespace Wms.UnitTests.Domain.ValueObjects;

public class DSCodeTests
{
    [Theory]
    [InlineData("DS12345678")]
    [InlineData("DS00000001")]
    [InlineData("DS99999999")]
    public void Create_ValidCode_ShouldSucceed(string validCode)
    {
        // Act
        var result = DSCode.Create(validCode);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(validCode);
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    [InlineData("DS")]
    [InlineData("DS1")]
    [InlineData("DS1234567")]
    [InlineData("DS123456789")]
    [InlineData("12345678")]
    [InlineData("ds12345678")]
    [InlineData("DX12345678")]
    [InlineData("DS1234567A")]
    [InlineData("DSABCDEFGH")]
    public void Create_InvalidCode_ShouldThrowException(string invalidCode)
    {
        // Act & Assert
        var exception = Assert.Throws<InvalidDSCodeException>(() => DSCode.Create(invalidCode));
        exception.Message.Should().Be($"Nieprawidłowy kod DS: '{invalidCode}'. Wymagany format: DS + 8 cyfr");
    }

    [Fact]
    public void TryCreate_ValidCode_ShouldReturnTrueAndCode()
    {
        // Arrange
        var validCode = "DS12345678";

        // Act
        var result = DSCode.TryCreate(validCode, out var dsCode);

        // Assert
        result.Should().BeTrue();
        dsCode.Should().NotBeNull();
        dsCode!.Value.Should().Be(validCode);
    }

    [Theory]
    [InlineData("")]
    [InlineData("DS1234567")]
    [InlineData("DSABCDEFGH")]
    public void TryCreate_InvalidCode_ShouldReturnFalseAndNull(string invalidCode)
    {
        // Act
        var result = DSCode.TryCreate(invalidCode, out var dsCode);

        // Assert
        result.Should().BeFalse();
        dsCode.Should().BeNull();
    }

    [Theory]
    [InlineData("DS12345678", true)]
    [InlineData("DS00000001", true)]
    [InlineData("DS99999999", true)]
    [InlineData("", false)]
    [InlineData("DS", false)]
    [InlineData("DS1234567", false)]
    [InlineData("DS123456789", false)]
    [InlineData("12345678", false)]
    [InlineData("ds12345678", false)]
    [InlineData("DSABCDEFGH", false)]
    [InlineData(null, false)]
    public void IsValid_ShouldReturnExpectedResult(string code, bool expected)
    {
        // Act
        var result = DSCode.IsValid(code);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void Equals_SameCodes_ShouldReturnTrue()
    {
        // Arrange
        var code1 = DSCode.Create("DS12345678");
        var code2 = DSCode.Create("DS12345678");

        // Act & Assert
        code1.Equals(code2).Should().BeTrue();
        (code1 == code2).Should().BeTrue();
        (code1 != code2).Should().BeFalse();
    }

    [Fact]
    public void Equals_DifferentCodes_ShouldReturnFalse()
    {
        // Arrange
        var code1 = DSCode.Create("DS12345678");
        var code2 = DSCode.Create("DS87654321");

        // Act & Assert
        code1.Equals(code2).Should().BeFalse();
        (code1 == code2).Should().BeFalse();
        (code1 != code2).Should().BeTrue();
    }

    [Fact]
    public void Equals_NullComparison_ShouldReturnFalse()
    {
        // Arrange
        var code = DSCode.Create("DS12345678");

        // Act & Assert
        code.Equals(null).Should().BeFalse();
        (code == null).Should().BeFalse();
        (code != null).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_SameCodes_ShouldReturnSameHashCode()
    {
        // Arrange
        var code1 = DSCode.Create("DS12345678");
        var code2 = DSCode.Create("DS12345678");

        // Act & Assert
        code1.GetHashCode().Should().Be(code2.GetHashCode());
    }

    [Fact]
    public void ToString_ShouldReturnValue()
    {
        // Arrange
        var code = DSCode.Create("DS12345678");

        // Act
        var result = code.ToString();

        // Assert
        result.Should().Be("DS12345678");
    }

    [Fact]
    public void ImplicitConversion_FromDSCodeToString_ShouldWork()
    {
        // Arrange
        var code = DSCode.Create("DS12345678");

        // Act
        string result = code;

        // Assert
        result.Should().Be("DS12345678");
    }

    [Fact]
    public void ExplicitConversion_FromStringToDSCode_ShouldWork()
    {
        // Arrange
        var codeString = "DS12345678";

        // Act
        var result = (DSCode)codeString;

        // Assert
        result.Value.Should().Be(codeString);
    }

    [Fact]
    public void ExplicitConversion_FromInvalidStringToDSCode_ShouldThrowException()
    {
        // Arrange
        var invalidCode = "INVALID";

        // Act & Assert
        Assert.Throws<InvalidDSCodeException>(() => (DSCode)invalidCode);
    }

    [Fact]
    public void ParsedValue_ShouldReturnCorrectNumericPart()
    {
        // Arrange
        var code = DSCode.Create("DS12345678");

        // Act
        var parsedValue = code.ParsedValue;

        // Assert
        parsedValue.Should().Be(12345678);
    }

    [Fact]
    public void ParsedValue_WithLeadingZeros_ShouldReturnCorrectValue()
    {
        // Arrange
        var code = DSCode.Create("DS00000123");

        // Act
        var parsedValue = code.ParsedValue;

        // Assert
        parsedValue.Should().Be(123);
    }

    [Theory]
    [InlineData("DS12345678", "DS12345678", 0)]
    [InlineData("DS00000001", "DS00000002", -1)]
    [InlineData("DS00000002", "DS00000001", 1)]
    [InlineData("DS99999999", "DS00000001", 1)]
    public void CompareTo_ShouldReturnExpectedResult(string code1, string code2, int expected)
    {
        // Arrange
        var dsCode1 = DSCode.Create(code1);
        var dsCode2 = DSCode.Create(code2);

        // Act
        var result = dsCode1.CompareTo(dsCode2);

        // Assert
        if (expected == 0)
            result.Should().Be(0);
        else if (expected > 0)
            result.Should().BePositive();
        else
            result.Should().BeNegative();
    }

    [Fact]
    public void CompareTo_WithNull_ShouldReturn1()
    {
        // Arrange
        var code = DSCode.Create("DS12345678");

        // Act
        var result = code.CompareTo(null);

        // Assert
        result.Should().Be(1);
    }

    [Theory]
    [InlineData("DS00000001", "DS00000002")]
    [InlineData("DS12345678", "DS99999999")]
    public void ComparisonOperators_ShouldWorkCorrectly(string smaller, string larger)
    {
        // Arrange
        var smallerCode = DSCode.Create(smaller);
        var largerCode = DSCode.Create(larger);

        // Act & Assert
        (smallerCode < largerCode).Should().BeTrue();
        (largerCode > smallerCode).Should().BeTrue();
        (smallerCode <= largerCode).Should().BeTrue();
        (largerCode >= smallerCode).Should().BeTrue();
        (smallerCode >= largerCode).Should().BeFalse();
        (largerCode <= smallerCode).Should().BeFalse();
    }
}
